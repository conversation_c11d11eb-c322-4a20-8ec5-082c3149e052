# 季节分布交互式过滤功能

## 功能概述

本功能实现了Seasonal Distribution组件与PatientClustering组件之间的交互式过滤，当用户在聚类可视化中使用矩形选择等工具选中患者时，季节分布图会动态更新显示选中患者的季节分布数据。

## 主要特性

### 🔄 实时交互过滤
- **初始状态**: 显示完整的季节分布数据
- **患者选择**: 使用brush工具选中患者后，季节分布图自动过滤
- **动态更新**: 选择不同患者时，季节分布实时更新
- **清空恢复**: 清空选择时恢复显示完整数据

### 📊 真实数据支持
- **后端数据源**: 从聚类API获取真实的季节分布数据
- **诊断代码同步**: 使用主要诊断代码进行数据过滤
- **多视图支持**: 支持季度和月度视图切换

### 🎯 智能过滤算法
- **诊断匹配**: 根据选中患者的主要诊断进行过滤
- **数据一致性**: 确保过滤结果与聚类数据保持同步
- **性能优化**: 高效的过滤算法，支持大量数据

## 实现架构

```
┌─────────────────┐    选择患者事件    ┌─────────────────┐
│ PatientClustering│ ──────────────▶ │ DiagnosisAssistant│
│   (brush选择)    │                 │   (事件处理)     │
└─────────────────┘                 └─────────────────┘
         │                                   │
         ▼                                   ▼
┌─────────────────┐                 ┌─────────────────┐
│   选中患者数据   │                 │   过滤季节数据   │
│  [患者1, 患者2]  │                 │ filterByDiagnoses│
└─────────────────┘                 └─────────────────┘
                                             │
                                             ▼
                                    ┌─────────────────┐
                                    │ Seasonal Distribution│
                                    │   (动态更新)     │
                                    └─────────────────┘
```

## 使用方法

### 1. 启动系统
```bash
# 启动后端
cd Bibm-master/Flask_end
python app.py

# 启动前端
cd Bibm-master
npm run dev
```

### 2. 交互操作
1. **查看完整数据**: 页面加载后，Seasonal Distribution显示所有患者的季节分布
2. **选择患者**: 在PatientClustering组件中使用工具栏的选择工具：
   - 🔲 矩形选择 (rect)
   - 🔺 多边形选择 (polygon) 
   - ⭕ 圆形选择 (circle)
   - ➖ 横向选择 (lineX)
   - ➖ 纵向选择 (lineY)
3. **查看过滤结果**: 选中患者后，Seasonal Distribution自动更新
4. **清空选择**: 使用"清除选择"工具或"删除最后选择框"恢复完整视图

### 3. 功能验证
在浏览器控制台中运行测试：
```javascript
// 运行完整测试
runSeasonalInteractiveTests();

// 单独测试后端交互
testSeasonalInteractiveFiltering();

// 单独测试前端交互
testFrontendInteraction();
```

## 技术实现

### 前端组件通信

#### PatientClustering.vue
```javascript
// 发出患者选择事件
handleBrushSelected(params) {
  // ... 处理选择逻辑
  this.$emit('patients-selected', selectedData);
}
```

#### DiagnosisAssistant.vue
```javascript
// 监听患者选择事件
<PatientClustering 
  @patients-selected="handlePatientsSelected" />

// 处理患者选择
handlePatientsSelected(selectedPatients) {
  this.selectedPatients = selectedPatients;
  this.filterSeasonalDataByPatients(selectedPatients);
}
```

### 数据过滤逻辑

```javascript
// 根据选中患者过滤季节数据
filterSeasonalDataByPatients(selectedPatients) {
  if (!selectedPatients || selectedPatients.length === 0) {
    // 显示完整数据
    this.filteredSeasonalData = null;
    return;
  }

  // 提取诊断代码
  const selectedDiagnoses = selectedPatients.map(patient => {
    return patient.diagnosis || patient.main_diagnosis || patient['主要诊断'];
  }).filter(diagnosis => diagnosis);

  // 过滤季节数据
  this.filteredSeasonalData = this.filterSeasonalDataByDiagnoses(selectedDiagnoses);
}
```

### 图表选项生成

```javascript
// 动态生成图表选项
seasonalDistributionOption() {
  const useRealData = this.filteredSeasonalData || this.realSeasonalData;
  
  if (useRealData) {
    return this.generateRealSeasonalOption(useRealData);
  }
  
  // 回退到模拟数据
  return this.generateMockSeasonalOption();
}
```

## 数据流程

### 1. 初始化阶段
```
页面加载 → 连接后端 → 加载聚类数据 → 加载季节分布数据 → 显示完整图表
```

### 2. 交互阶段
```
用户选择患者 → 触发brush事件 → 发出选择事件 → 过滤季节数据 → 更新图表显示
```

### 3. 恢复阶段
```
清空选择 → 发出空选择事件 → 重置过滤数据 → 恢复完整图表
```

## 配置选项

### 季节分布配置
```javascript
const seasonalConfig = {
  viewMode: 'quarter',        // 'quarter' | 'month'
  autoFilter: true,           // 自动过滤功能
  showTitle: true,            // 显示标题
  showSelectedCount: true     // 显示选中患者数量
};
```

### 过滤配置
```javascript
const filterConfig = {
  useMainDiagnosis: true,     // 使用主要诊断进行过滤
  includeSecondary: false,    // 是否包含次要诊断
  minPatientCount: 1          // 最小患者数量阈值
};
```

## 故障排除

### 常见问题

#### 1. 季节分布不更新
**症状**: 选择患者后季节分布图没有变化
**解决方案**:
- 检查PatientClustering组件是否正确发出`patients-selected`事件
- 确认DiagnosisAssistant组件是否正确监听事件
- 查看浏览器控制台是否有错误信息

#### 2. 过滤结果为空
**症状**: 选择患者后季节分布图变为空白
**解决方案**:
- 检查选中患者的诊断数据是否有效
- 确认季节分布数据中是否包含对应的诊断
- 验证诊断代码格式是否一致

#### 3. 性能问题
**症状**: 选择大量患者时响应缓慢
**解决方案**:
- 限制同时选择的患者数量
- 优化过滤算法
- 添加防抖机制

### 调试方法

#### 启用调试日志
```javascript
// 在浏览器控制台中启用详细日志
localStorage.setItem('debug-seasonal-filter', 'true');
```

#### 检查数据状态
```javascript
// 检查当前选中的患者
console.log('选中患者:', this.selectedPatients);

// 检查季节分布数据
console.log('完整数据:', this.realSeasonalData);
console.log('过滤数据:', this.filteredSeasonalData);
```

## 扩展功能

### 计划中的功能
- 🔄 支持多选择框的并集/交集操作
- 📊 添加选择统计信息显示
- 🎨 自定义过滤条件
- 📈 选择历史记录
- 🔍 搜索特定诊断的患者

### 自定义扩展
开发者可以通过以下方式扩展功能：

1. **自定义过滤逻辑**
```javascript
// 添加自定义过滤方法
customFilterSeasonalData(patients, criteria) {
  // 实现自定义过滤逻辑
}
```

2. **添加新的交互方式**
```javascript
// 监听其他组件的事件
@disease-selected="handleDiseaseSelected"
@time-range-changed="handleTimeRangeChanged"
```

3. **扩展数据源**
```javascript
// 支持多种数据源
async loadSeasonalDataFromSource(source) {
  // 实现多数据源支持
}
```

## 总结

季节分布交互式过滤功能为用户提供了直观的数据探索体验，通过简单的图形选择操作就能深入分析特定患者群体的季节分布模式。该功能充分利用了真实的聚类数据，确保了分析结果的准确性和实用性。
