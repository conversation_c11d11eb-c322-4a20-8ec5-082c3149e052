#!/usr/bin/env python3
"""
验证All.json数据中的主要诊断字段
检查"主要诊断"和"主要诊断名称"字段的内容
"""

import json
import os
from collections import Counter

def verify_diagnosis_fields():
    """验证诊断字段内容"""
    
    # 构建数据文件路径
    current_dir = os.path.dirname(__file__)
    data_path = os.path.join(current_dir, 'Bibm-master', 'Flask_end', 'data', 'All.json')
    
    if not os.path.exists(data_path):
        # 尝试另一个路径
        data_path = os.path.join(current_dir, 'Bibm-master', 'data', 'All.json')
    
    if not os.path.exists(data_path):
        print(f"❌ 找不到数据文件: {data_path}")
        return False
    
    try:
        print(f"📂 加载数据文件: {data_path}")
        
        with open(data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ 成功加载 {len(data)} 条记录")
        
        # 检查字段存在情况
        main_diagnosis_count = 0
        main_diagnosis_name_count = 0
        secondary_diagnosis_code_count = 0
        secondary_diagnosis_name_count = 0
        
        main_diagnosis_samples = []
        main_diagnosis_name_samples = []
        secondary_diagnosis_code_samples = []
        secondary_diagnosis_name_samples = []
        
        for i, record in enumerate(data[:100]):  # 检查前100条记录
            # 主要诊断
            if '主要诊断' in record and record['主要诊断']:
                main_diagnosis_count += 1
                if len(main_diagnosis_samples) < 5:
                    main_diagnosis_samples.append(record['主要诊断'])
            
            # 主要诊断名称
            if '主要诊断名称' in record and record['主要诊断名称']:
                main_diagnosis_name_count += 1
                if len(main_diagnosis_name_samples) < 5:
                    main_diagnosis_name_samples.append(record['主要诊断名称'])
            
            # 次要诊断代码
            if '次要诊断代码' in record and record['次要诊断代码']:
                secondary_diagnosis_code_count += 1
                if len(secondary_diagnosis_code_samples) < 5:
                    secondary_diagnosis_code_samples.append(record['次要诊断代码'])
            
            # 次要诊断名称
            if '次要诊断名称' in record and record['次要诊断名称']:
                secondary_diagnosis_name_count += 1
                if len(secondary_diagnosis_name_samples) < 5:
                    secondary_diagnosis_name_samples.append(record['次要诊断名称'])
        
        print("\n📊 字段统计 (前100条记录):")
        print(f"   主要诊断: {main_diagnosis_count}/100 条记录有值")
        print(f"   主要诊断名称: {main_diagnosis_name_count}/100 条记录有值")
        print(f"   次要诊断代码: {secondary_diagnosis_code_count}/100 条记录有值")
        print(f"   次要诊断名称: {secondary_diagnosis_name_count}/100 条记录有值")
        
        print("\n📋 字段内容示例:")
        
        if main_diagnosis_samples:
            print(f"   主要诊断 (代码): {main_diagnosis_samples}")
        else:
            print("   主要诊断: 无数据")
        
        if main_diagnosis_name_samples:
            print(f"   主要诊断名称: {main_diagnosis_name_samples}")
        else:
            print("   主要诊断名称: 无数据")
        
        if secondary_diagnosis_code_samples:
            print(f"   次要诊断代码: {secondary_diagnosis_code_samples}")
        else:
            print("   次要诊断代码: 无数据")
        
        if secondary_diagnosis_name_samples:
            print(f"   次要诊断名称: {secondary_diagnosis_name_samples}")
        else:
            print("   次要诊断名称: 无数据")
        
        # 分析主要诊断字段的格式
        print("\n🔍 主要诊断字段格式分析:")
        
        if main_diagnosis_samples:
            # 检查是否为ICD代码格式
            icd_pattern_count = sum(1 for code in main_diagnosis_samples if 
                                  isinstance(code, str) and len(code) > 0 and 
                                  (code[0].isupper() and any(c.isdigit() for c in code)))
            
            print(f"   ICD代码格式样本: {icd_pattern_count}/{len(main_diagnosis_samples)}")
            
            if icd_pattern_count > 0:
                print("   ✅ 主要诊断字段包含代码格式数据")
            else:
                print("   ⚠️ 主要诊断字段可能不是代码格式")
        
        # 统计所有唯一的主要诊断
        print("\n📈 统计所有唯一主要诊断:")
        
        all_main_diagnoses = []
        all_main_diagnosis_names = []
        
        for record in data:
            if '主要诊断' in record and record['主要诊断']:
                all_main_diagnoses.append(record['主要诊断'])
            
            if '主要诊断名称' in record and record['主要诊断名称']:
                all_main_diagnosis_names.append(record['主要诊断名称'])
        
        unique_main_diagnoses = list(set(all_main_diagnoses))
        unique_main_diagnosis_names = list(set(all_main_diagnosis_names))
        
        print(f"   唯一主要诊断代码: {len(unique_main_diagnoses)} 种")
        print(f"   唯一主要诊断名称: {len(unique_main_diagnosis_names)} 种")
        
        # 显示最常见的诊断
        if all_main_diagnoses:
            diagnosis_counter = Counter(all_main_diagnoses)
            top_diagnoses = diagnosis_counter.most_common(5)
            print(f"\n🔝 最常见的主要诊断代码:")
            for diagnosis, count in top_diagnoses:
                print(f"   {diagnosis}: {count} 次")
        
        if all_main_diagnosis_names:
            name_counter = Counter(all_main_diagnosis_names)
            top_names = name_counter.most_common(5)
            print(f"\n🔝 最常见的主要诊断名称:")
            for name, count in top_names:
                print(f"   {name}: {count} 次")
        
        # 建议
        print("\n💡 建议:")
        if main_diagnosis_count > main_diagnosis_name_count:
            print("   ✅ 建议使用'主要诊断'字段作为Case Selection的数据源")
        elif main_diagnosis_name_count > main_diagnosis_count:
            print("   ✅ 建议使用'主要诊断名称'字段作为Case Selection的数据源")
        else:
            print("   ⚠️ 两个字段的数据量相近，需要根据具体需求选择")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔍 开始验证All.json中的诊断字段...")
    print("=" * 60)
    
    success = verify_diagnosis_fields()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 诊断字段验证完成")
    else:
        print("❌ 诊断字段验证失败")

if __name__ == "__main__":
    main()
