#!/usr/bin/env python3
"""
疾病相关性分析
使用Apriori算法分析患者主要诊断和次要诊断之间的相关性
并生成热力图展示相关性强度
"""

import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
from mlxtend.frequent_patterns import apriori
from mlxtend.frequent_patterns import association_rules
from typing import List, Dict, Set, Tuple

class DiseaseCorrelationAnalyzer:
    """疾病相关性分析器"""
    
    def __init__(self, min_support=0.01, min_confidence=0.1, min_lift=1.0):
        """
        初始化分析器
        
        Args:
            min_support: 最小支持度阈值
            min_confidence: 最小置信度阈值
            min_lift: 最小提升度阈值
        """
        self.min_support = min_support
        self.min_confidence = min_confidence
        self.min_lift = min_lift
        self.transactions = []
        self.disease_sets = []
        self.rules = None
        self.correlation_matrix = None
        self.main_diagnoses = set()
        self.secondary_diagnoses = set()
        
    def load_data(self, file_path: str) -> List[Dict]:
        """
        从JSON文件加载患者数据
        
        Args:
            file_path: JSON文件路径
            
        Returns:
            患者数据列表
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    
    def prepare_transactions(self, data: List[Dict]) -> None:
        """
        准备事务数据，每个事务是一个患者的所有诊断代码集合
        
        Args:
            data: 患者数据列表
        """
        self.transactions = []
        self.main_diagnoses = set()
        self.secondary_diagnoses = set()
        
        for patient in data:
            # 提取主要诊断
            main_diagnosis = patient.get('主要诊断') or patient.get('主要诊断名称')
            
            # 提取次要诊断
            secondary_diagnoses = []
            secondary_codes = patient.get('次要诊断代码', '')
            
            # 处理次要诊断代码（可能是逗号分隔的）
            if secondary_codes:
                if isinstance(secondary_codes, str):
                    secondary_diagnoses.extend([d.strip() for d in secondary_codes.split(',') if d.strip()])
                elif isinstance(secondary_codes, list):
                    secondary_diagnoses.extend([str(d).strip() for d in secondary_codes if str(d).strip()])
                else:
                    secondary_diagnoses.append(str(secondary_codes))
            
            # 创建事务（疾病集合）
            transaction = set()
            
            if main_diagnosis:
                transaction.add(main_diagnosis)
                self.main_diagnoses.add(main_diagnosis)
            
            for secondary in secondary_diagnoses:
                if secondary and secondary != main_diagnosis:  # 避免重复
                    transaction.add(secondary)
                    self.secondary_diagnoses.add(secondary)
            
            # 只保留包含至少一个疾病的事务
            if len(transaction) > 0:
                self.transactions.append(transaction)
                self.disease_sets.append({
                    'main_diagnosis': main_diagnosis,
                    'secondary_diagnoses': list(set(secondary_diagnoses))
                })
        
        print(f"准备了 {len(self.transactions)} 个事务")
        print(f"主要诊断种类: {len(self.main_diagnoses)}")
        print(f"次要诊断种类: {len(self.secondary_diagnoses)}")
    
    def create_one_hot_encoding(self) -> pd.DataFrame:
        """
        创建诊断代码的one-hot编码
        
        Returns:
            包含one-hot编码的DataFrame
        """
        # 获取所有唯一的诊断代码
        all_diseases = set()
        for transaction in self.transactions:
            all_diseases.update(transaction)
        
        # 创建one-hot编码DataFrame
        one_hot = pd.DataFrame(index=range(len(self.transactions)), columns=list(all_diseases))
        one_hot = one_hot.fillna(0)
        
        # 填充one-hot编码
        for i, transaction in enumerate(self.transactions):
            for disease in transaction:
                one_hot.at[i, disease] = 1
        
        return one_hot
    
    def run_apriori_analysis(self) -> pd.DataFrame:
        """
        运行Apriori分析并生成关联规则
        
        Returns:
            关联规则DataFrame
        """
        # 创建one-hot编码
        one_hot = self.create_one_hot_encoding()
        
        # 使用mlxtend库的apriori函数找出频繁项集
        print(f"使用最小支持度 {self.min_support} 挖掘频繁项集...")
        frequent_itemsets = apriori(one_hot, min_support=self.min_support, use_colnames=True)
        print(f"找到 {len(frequent_itemsets)} 个频繁项集")
        
        # 生成关联规则
        print(f"使用最小置信度 {self.min_confidence} 和最小提升度 {self.min_lift} 生成关联规则...")
        rules = association_rules(frequent_itemsets, 
                                 metric="confidence", 
                                 min_threshold=self.min_confidence)
        
        # 筛选满足最小提升度的规则
        rules = rules[rules['lift'] >= self.min_lift]
        
        # 筛选只包含单个前件和单个后件的规则
        rules = rules[rules['antecedents'].apply(lambda x: len(x) == 1) & 
                     rules['consequents'].apply(lambda x: len(x) == 1)]
        
        # 转换frozenset为列表
        rules['antecedent'] = rules['antecedents'].apply(lambda x: list(x)[0])
        rules['consequent'] = rules['consequents'].apply(lambda x: list(x)[0])
        
        # 筛选主要诊断→次要诊断的规则
        main_to_secondary_rules = rules[rules['antecedent'].isin(self.main_diagnoses) & 
                                       rules['consequent'].isin(self.secondary_diagnoses)]
        
        print(f"生成了 {len(main_to_secondary_rules)} 条主要诊断→次要诊断的关联规则")
        self.rules = main_to_secondary_rules
        
        return main_to_secondary_rules
    
    def create_correlation_matrix(self, top_n: int = 20) -> Tuple[pd.DataFrame, List[str], List[str]]:
        """
        创建相关性矩阵
        
        Args:
            top_n: 选取的主要诊断和次要诊断的数量
            
        Returns:
            相关性矩阵DataFrame，主要诊断列表，次要诊断列表
        """
        if self.rules is None:
            self.run_apriori_analysis()
        
        # 获取最常见的主要诊断和次要诊断
        main_diagnosis_counter = Counter()
        secondary_diagnosis_counter = Counter()
        
        for disease_set in self.disease_sets:
            main_diagnosis = disease_set['main_diagnosis']
            if main_diagnosis:
                main_diagnosis_counter[main_diagnosis] += 1
            
            for secondary in disease_set['secondary_diagnoses']:
                if secondary:
                    secondary_diagnosis_counter[secondary] += 1
        
        top_main_diagnoses = [d for d, _ in main_diagnosis_counter.most_common(top_n)]
        top_secondary_diagnoses = [d for d, _ in secondary_diagnosis_counter.most_common(top_n)]
        
        # 创建相关性矩阵
        matrix = pd.DataFrame(index=top_main_diagnoses, columns=top_secondary_diagnoses)
        matrix = matrix.fillna(0.0)
        
        # 填充相关性矩阵
        for _, rule in self.rules.iterrows():
            main_diagnosis = rule['antecedent']
            secondary_diagnosis = rule['consequent']
            
            if main_diagnosis in top_main_diagnoses and secondary_diagnosis in top_secondary_diagnoses:
                matrix.at[main_diagnosis, secondary_diagnosis] = rule['lift']
        
        self.correlation_matrix = matrix
        return matrix, top_main_diagnoses, top_secondary_diagnoses
    
    def plot_heatmap(self, output_file: str = 'disease_correlation_heatmap.png') -> None:
        """
        绘制相关性热力图
        
        Args:
            output_file: 输出文件路径
        """
        if self.correlation_matrix is None:
            self.create_correlation_matrix()
        
        plt.figure(figsize=(12, 10))
        
        # 绘制热力图
        ax = sns.heatmap(self.correlation_matrix, annot=True, fmt='.1f', cmap='Blues',
                        linewidths=0.5, cbar_kws={'label': 'Lift (提升度)'})
        
        plt.title('Disease Correlation (主要诊断 → 次要诊断)', fontsize=16)
        plt.ylabel('主要诊断', fontsize=14)
        plt.xlabel('次要诊断', fontsize=14)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图像
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"热力图已保存到 {output_file}")
        
        # 显示图像
        plt.show()

def main():
    # 设置参数
    min_support = 0.01
    min_confidence = 0.1
    min_lift = 1.0
    top_n = 15
    
    # 创建分析器
    analyzer = DiseaseCorrelationAnalyzer(
        min_support=min_support,
        min_confidence=min_confidence,
        min_lift=min_lift
    )
    
    # 加载数据
    data = analyzer.load_data('Bibm-master/Flask_end/data/patient_records.json')
    
    # 准备事务数据
    analyzer.prepare_transactions(data)
    
    # 运行Apriori分析
    analyzer.run_apriori_analysis()
    
    # 创建相关性矩阵
    analyzer.create_correlation_matrix(top_n=top_n)
    
    # 绘制热力图
    analyzer.plot_heatmap()

if __name__ == '__main__':
    main() 