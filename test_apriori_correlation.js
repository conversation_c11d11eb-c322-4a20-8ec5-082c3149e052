/**
 * 测试Apriori关联规则分析和疾病关联热力图功能
 * 验证主要诊断和次要诊断代码的关联关系分析
 */

// 测试函数：验证Apriori关联规则分析
async function testAprioriCorrelationAnalysis() {
  console.log('🧪 开始测试Apriori关联规则分析功能...');
  
  try {
    // 1. 检查后端连接
    console.log('1️⃣ 检查后端连接...');
    const connectionStatus = await patientDataProcessor.checkBackendConnection();
    
    if (!connectionStatus.connected) {
      console.error('❌ 后端未连接');
      return false;
    }
    console.log('✅ 后端连接正常');
    
    // 2. 测试疾病关联分析API
    console.log('2️⃣ 测试疾病关联分析API...');
    
    const correlationOptions = {
      minSupport: 0.01,
      minConfidence: 0.1,
      minLift: 1.0,
      topDiseases: 15
    };
    
    const correlationData = await patientDataProcessor.getDiseaseCorrelation(correlationOptions);
    
    if (!correlationData) {
      console.error('❌ 疾病关联分析数据获取失败');
      return false;
    }
    
    console.log('✅ 疾病关联分析数据获取成功');
    console.log('📊 分析结果概览:');
    console.log(`   关联矩阵大小: ${correlationData.correlation_matrix.size}x${correlationData.correlation_matrix.size}`);
    console.log(`   疾病数量: ${correlationData.correlation_matrix.diseases.length}`);
    console.log(`   关联规则数量: ${correlationData.association_rules.length}`);
    console.log(`   总事务数: ${correlationData.summary.total_transactions}`);
    console.log(`   总疾病种类: ${correlationData.summary.total_diseases}`);
    
    // 3. 分析关联矩阵
    console.log('3️⃣ 分析关联矩阵...');
    
    const matrix = correlationData.correlation_matrix;
    const diseases = matrix.diseases;
    const correlationMatrix = matrix.matrix;
    
    console.log('📋 疾病列表:');
    diseases.forEach((disease, index) => {
      console.log(`   ${index + 1}. ${disease}`);
    });
    
    // 找出最强的关联关系
    let maxCorrelation = 0;
    let maxPair = null;
    
    for (let i = 0; i < diseases.length; i++) {
      for (let j = i + 1; j < diseases.length; j++) {
        const correlation = Math.max(correlationMatrix[i][j], correlationMatrix[j][i]);
        if (correlation > maxCorrelation) {
          maxCorrelation = correlation;
          maxPair = [diseases[i], diseases[j]];
        }
      }
    }
    
    if (maxPair) {
      console.log(`🔗 最强关联关系: ${maxPair[0]} ↔ ${maxPair[1]} (${maxCorrelation.toFixed(3)})`);
    }
    
    // 4. 分析关联规则
    console.log('4️⃣ 分析关联规则...');
    
    const rules = correlationData.association_rules;
    
    if (rules.length > 0) {
      console.log('📋 前5条关联规则:');
      rules.slice(0, 5).forEach((rule, index) => {
        const antecedent = rule.antecedent.join(' & ');
        const consequent = rule.consequent.join(' & ');
        console.log(`   ${index + 1}. ${antecedent} → ${consequent}`);
        console.log(`      支持度: ${rule.support.toFixed(3)}, 置信度: ${rule.confidence.toFixed(3)}, 提升度: ${rule.lift.toFixed(3)}`);
      });
    } else {
      console.log('⚠️ 没有找到满足条件的关联规则');
    }
    
    // 5. 测试关联规则API
    console.log('5️⃣ 测试关联规则API...');
    
    const rulesOptions = {
      minSupport: 0.01,
      minConfidence: 0.1,
      minLift: 1.0,
      limit: 50
    };
    
    const rulesData = await patientDataProcessor.getAssociationRules(rulesOptions);
    
    if (rulesData && rulesData.rules) {
      console.log(`✅ 关联规则数据获取成功，共 ${rulesData.rules.length} 条规则`);
      
      // 显示规则统计
      const confidenceStats = rulesData.rules.map(r => r.confidence);
      const liftStats = rulesData.rules.map(r => r.lift);
      
      console.log('📊 规则统计:');
      console.log(`   平均置信度: ${(confidenceStats.reduce((a, b) => a + b, 0) / confidenceStats.length).toFixed(3)}`);
      console.log(`   平均提升度: ${(liftStats.reduce((a, b) => a + b, 0) / liftStats.length).toFixed(3)}`);
      console.log(`   最高置信度: ${Math.max(...confidenceStats).toFixed(3)}`);
      console.log(`   最高提升度: ${Math.max(...liftStats).toFixed(3)}`);
    } else {
      console.log('⚠️ 关联规则数据获取失败');
    }
    
    // 6. 测试热力图数据格式
    console.log('6️⃣ 测试热力图数据格式...');
    
    // 模拟生成热力图数据
    const mockGenerateHeatmapData = (matrix) => {
      const diseases = matrix.diseases;
      const correlationMatrix = matrix.matrix;
      const heatmapData = [];
      
      for (let i = 0; i < diseases.length; i++) {
        for (let j = 0; j < diseases.length; j++) {
          heatmapData.push([i, j, correlationMatrix[i][j]]);
        }
      }
      
      return {
        diseases: diseases,
        data: heatmapData,
        size: diseases.length
      };
    };
    
    const heatmapData = mockGenerateHeatmapData(matrix);
    
    console.log('📊 热力图数据格式验证:');
    console.log(`   数据点数量: ${heatmapData.data.length}`);
    console.log(`   矩阵大小: ${heatmapData.size}x${heatmapData.size}`);
    console.log(`   数据范围: ${Math.min(...heatmapData.data.map(d => d[2])).toFixed(3)} - ${Math.max(...heatmapData.data.map(d => d[2])).toFixed(3)}`);
    
    // 显示部分数据样本
    console.log('📋 数据样本 (前5个):');
    heatmapData.data.slice(0, 5).forEach((point, index) => {
      const [x, y, value] = point;
      console.log(`   ${index + 1}. [${x}, ${y}] = ${value.toFixed(3)} (${diseases[x]} → ${diseases[y]})`);
    });
    
    // 7. 验证参数影响
    console.log('7️⃣ 验证参数影响...');
    
    const parameterTests = [
      { minSupport: 0.005, minConfidence: 0.05, description: '低阈值' },
      { minSupport: 0.02, minConfidence: 0.2, description: '中等阈值' },
      { minSupport: 0.05, minConfidence: 0.5, description: '高阈值' }
    ];
    
    for (const test of parameterTests) {
      try {
        const testData = await patientDataProcessor.getDiseaseCorrelation({
          minSupport: test.minSupport,
          minConfidence: test.minConfidence,
          minLift: 1.0,
          topDiseases: 10
        });
        
        console.log(`   ${test.description} (support=${test.minSupport}, confidence=${test.minConfidence}):`);
        console.log(`     关联规则: ${testData.association_rules.length} 条`);
        console.log(`     疾病数量: ${testData.correlation_matrix.diseases.length} 种`);
      } catch (error) {
        console.log(`   ${test.description}: 测试失败 - ${error.message}`);
      }
    }
    
    console.log('🎉 Apriori关联规则分析功能测试完成！');
    
    return {
      correlationData,
      rulesData,
      heatmapData,
      testResults: {
        apiWorking: true,
        dataValid: true,
        rulesGenerated: rules.length > 0,
        matrixGenerated: matrix.size > 0
      }
    };
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    return false;
  }
}

// 测试函数：验证前端热力图集成
function testHeatmapIntegration() {
  console.log('🎭 测试前端热力图集成...');
  
  // 模拟DiagnosisAssistant组件的热力图生成
  const mockGenerateRealCorrelationOption = (correlationMatrix) => {
    const diseases = correlationMatrix.diseases;
    const matrix = correlationMatrix.matrix;
    
    // 创建热力图数据
    const heatmapData = [];
    for (let i = 0; i < diseases.length; i++) {
      for (let j = 0; j < diseases.length; j++) {
        heatmapData.push([i, j, matrix[i][j]]);
      }
    }

    // 生成疾病简称
    const diseaseLabels = diseases.map(disease => {
      if (disease.length > 8) {
        return disease.substring(0, 6) + '..';
      }
      return disease;
    });

    return {
      title: {
        text: 'Disease Correlation (Apriori Analysis)',
        left: 'center'
      },
      xAxis: {
        type: "category",
        data: diseaseLabels
      },
      yAxis: {
        type: "category", 
        data: diseaseLabels
      },
      series: [{
        name: "Disease Correlation",
        type: "heatmap",
        data: heatmapData
      }]
    };
  };
  
  // 测试数据
  const testMatrix = {
    diseases: ['H35.804', 'H40.11', 'H25.9', 'E11.3'],
    matrix: [
      [1.0, 0.3, 0.2, 0.8],
      [0.3, 1.0, 0.7, 0.4],
      [0.2, 0.7, 1.0, 0.3],
      [0.8, 0.4, 0.3, 1.0]
    ]
  };
  
  const chartOption = mockGenerateRealCorrelationOption(testMatrix);
  
  console.log('📊 热力图配置验证:');
  console.log(`   标题: ${chartOption.title.text}`);
  console.log(`   X轴标签数量: ${chartOption.xAxis.data.length}`);
  console.log(`   Y轴标签数量: ${chartOption.yAxis.data.length}`);
  console.log(`   数据点数量: ${chartOption.series[0].data.length}`);
  
  // 验证数据完整性
  const expectedDataPoints = testMatrix.diseases.length * testMatrix.diseases.length;
  const actualDataPoints = chartOption.series[0].data.length;
  
  console.log('✅ 数据完整性验证:');
  console.log(`   期望数据点: ${expectedDataPoints}`);
  console.log(`   实际数据点: ${actualDataPoints}`);
  console.log(`   数据完整: ${expectedDataPoints === actualDataPoints ? '✅' : '❌'}`);
  
  return true;
}

// 主测试函数
async function runAprioriCorrelationTests() {
  console.log('🚀 开始运行Apriori关联规则分析测试...');
  console.log('=' * 60);
  
  // 测试1: 后端Apriori分析
  const backendTest = await testAprioriCorrelationAnalysis();
  
  console.log('\n' + '=' * 60);
  
  // 测试2: 前端热力图集成
  const frontendTest = testHeatmapIntegration();
  
  console.log('\n' + '=' * 60);
  console.log('📊 测试结果汇总:');
  console.log(`   后端Apriori分析: ${backendTest ? '✅ 通过' : '❌ 失败'}`);
  console.log(`   前端热力图集成: ${frontendTest ? '✅ 通过' : '❌ 失败'}`);
  
  if (backendTest && frontendTest) {
    console.log('\n🎉 所有测试通过！');
    console.log('💡 功能特点:');
    console.log('   - 使用Apriori算法分析主要诊断和次要诊断的关联关系');
    console.log('   - 生成疾病关联矩阵和热力图');
    console.log('   - 提供详细的关联规则和统计信息');
    console.log('   - 支持参数调整和实时分析');
    console.log('   - 与聚类数据完全集成');
  } else {
    console.log('⚠️ 部分测试失败，请检查相关功能');
  }
  
  console.log('\n📋 使用说明:');
  console.log('   1. 确保后端服务正常运行');
  console.log('   2. 加载聚类数据');
  console.log('   3. 查看Disease Correlation面板的热力图');
  console.log('   4. 热力图显示疾病间的关联强度');
  console.log('   5. 可以调整Apriori算法参数进行不同分析');
}

// 导出测试函数供浏览器控制台使用
if (typeof window !== 'undefined') {
  window.testAprioriCorrelationAnalysis = testAprioriCorrelationAnalysis;
  window.testHeatmapIntegration = testHeatmapIntegration;
  window.runAprioriCorrelationTests = runAprioriCorrelationTests;
  
  console.log('🧪 Apriori关联规则分析测试函数已加载到全局作用域');
  console.log('💡 使用方法:');
  console.log('   - runAprioriCorrelationTests() - 运行所有测试');
  console.log('   - testAprioriCorrelationAnalysis() - 测试后端Apriori分析');
  console.log('   - testHeatmapIntegration() - 测试前端热力图集成');
}

export { testAprioriCorrelationAnalysis, testHeatmapIntegration, runAprioriCorrelationTests };
