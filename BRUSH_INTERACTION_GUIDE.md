# 矩形选择与季节分布交互功能使用指南

## 功能概述

本功能实现了PatientClustering组件与Seasonal Distribution组件之间的实时交互。当您在聚类可视化中使用矩形选择等工具选中患者时，季节分布图会自动更新，只显示选中患者的季节分布数据。

## 🎯 核心特性

### 初始状态
- **完整数据显示**: 页面加载时，Seasonal Distribution显示所有患者的完整季节分布
- **真实数据**: 使用从后端API获取的真实聚类和季节分布数据
- **季度视图**: 默认显示Q1, Q2, Q3, Q4的季度分布

### 交互选择
- **多种选择工具**: 支持矩形、多边形、圆形、横向、纵向选择
- **实时过滤**: 选择患者后，季节分布立即更新
- **多选支持**: 可以创建多个选择区域
- **选择管理**: 支持保持选择、清除选择、删除最后选择框

### 动态更新
- **智能过滤**: 根据选中患者的主要诊断代码过滤季节数据
- **颜色一致**: 使用与聚类图一致的颜色方案
- **标题更新**: 显示选中患者数量
- **恢复机制**: 清空选择时自动恢复完整数据

## 🛠️ 使用方法

### 1. 基本操作流程

```
1. 页面加载 → 显示完整季节分布
2. 选择工具 → 在聚类图中选择患者
3. 实时更新 → 季节分布自动过滤
4. 清空选择 → 恢复完整数据显示
```

### 2. 选择工具使用

#### 🔲 矩形选择 (推荐)
- 点击工具栏中的"矩形选择"按钮
- 在聚类图中拖拽绘制矩形
- 释放鼠标完成选择

#### 🔺 多边形选择
- 点击工具栏中的"多边形选择"按钮
- 在聚类图中点击多个点形成多边形
- 双击完成选择

#### ⭕ 圆形选择
- 点击工具栏中的"圆形选择"按钮
- 在聚类图中拖拽绘制圆形
- 释放鼠标完成选择

#### ➖ 横向/纵向选择
- 选择"横向选择"或"纵向选择"
- 在图表中拖拽绘制选择线
- 选中线条范围内的所有患者

### 3. 选择管理

#### 保持选择
- 点击"保持选择"按钮
- 当前选择将被锁定
- 可以继续添加新的选择区域

#### 清除选择
- 点击"清除选择"按钮
- 清空所有选择区域
- 季节分布恢复显示完整数据

#### 删除最后选择框
- 点击"删除最后选择框"按钮（❌图标）
- 只删除最近创建的选择区域
- 保留其他选择区域

## 📊 数据流程

### 选择流程
```
用户拖拽矩形 → brush事件触发 → 获取选中患者 → 提取诊断代码 → 过滤季节数据 → 更新图表
```

### 数据过滤逻辑
```javascript
// 1. 提取选中患者的诊断代码
const selectedDiagnoses = selectedPatients.map(patient => {
  return patient.diagnosis || patient.main_diagnosis || patient['主要诊断'];
}).filter(diagnosis => diagnosis);

// 2. 基于诊断代码过滤季节数据
const filteredData = {
  ...seasonalData,
  series: seasonalData.series.filter(series => 
    diagnosisSet.has(series.name)
  )
};

// 3. 更新图表显示
this.filteredSeasonalData = filteredData;
```

## 🎨 视觉效果

### 颜色方案
- **一致性**: 季节分布使用与聚类图相同的颜色
- **区分度**: 使用高对比度颜色确保清晰可见
- **渐变效果**: 柱状图带有圆角和阴影效果

### 标题动态更新
- **完整数据**: "Seasonal Distribution (Complete Data)"
- **选择状态**: "Seasonal Distribution (X patients selected)"

### 图例和提示
- **滚动图例**: 支持大量疾病的显示
- **交互提示**: 鼠标悬停显示详细数据
- **轴标签**: 清晰的季度标签 (Q1, Q2, Q3, Q4)

## 🔧 技术实现

### 组件通信
```javascript
// PatientClustering.vue - 发出事件
this.$emit('patients-selected', selectedData);

// DiagnosisAssistant.vue - 监听事件
<PatientClustering @patients-selected="handlePatientsSelected" />
```

### 事件处理
```javascript
// 处理患者选择
handlePatientsSelected(selectedPatients) {
  this.selectedPatients = selectedPatients;
  this.filterSeasonalDataByPatients(selectedPatients);
}

// 过滤季节数据
filterSeasonalDataByPatients(selectedPatients) {
  if (!selectedPatients || selectedPatients.length === 0) {
    this.filteredSeasonalData = null; // 显示完整数据
    return;
  }
  
  // 提取诊断并过滤
  const selectedDiagnoses = selectedPatients.map(p => p.diagnosis);
  this.filteredSeasonalData = this.filterSeasonalDataByDiagnoses(selectedDiagnoses);
}
```

## 🧪 测试验证

### 浏览器控制台测试
```javascript
// 运行完整测试
runBrushInteractionTests();

// 单独测试功能
testBrushInteraction();
testBrushEventSimulation();
```

### 手动测试步骤
1. **初始验证**: 确认页面加载后显示完整季节分布
2. **选择测试**: 使用矩形工具选择部分患者
3. **更新验证**: 确认季节分布图自动更新
4. **清空测试**: 使用清除工具恢复完整显示
5. **多选测试**: 创建多个选择区域验证效果

## ⚠️ 注意事项

### 数据要求
- 患者数据必须包含诊断信息 (`diagnosis`, `main_diagnosis`, `主要诊断`)
- 季节分布数据必须与患者诊断代码匹配
- 后端服务必须正常运行

### 性能考虑
- 大量患者选择时可能有轻微延迟
- 建议单次选择患者数量控制在100个以内
- 使用防抖机制避免频繁更新

### 兼容性
- 支持现代浏览器 (Chrome, Firefox, Safari, Edge)
- 需要JavaScript启用
- 响应式设计，支持不同屏幕尺寸

## 🚀 高级功能

### 快捷键支持
- **Ctrl+A**: 全选所有患者
- **Escape**: 清除当前选择
- **Delete**: 删除最后选择框

### 批量操作
- 支持同时选择多个聚类
- 可以基于特定条件批量选择患者
- 支持选择历史记录

### 数据导出
- 可以导出选中患者的详细信息
- 支持导出过滤后的季节分布数据
- 提供CSV和JSON格式

## 📈 使用场景

### 临床研究
- 分析特定疾病的季节性模式
- 比较不同患者群体的就诊趋势
- 识别季节性疾病爆发

### 数据探索
- 快速筛选感兴趣的患者群体
- 交互式数据分析
- 模式发现和假设验证

### 报告生成
- 生成特定患者群体的季节分布报告
- 创建可视化分析图表
- 支持决策制定

## 🔮 未来扩展

### 计划功能
- 支持时间范围选择
- 添加更多过滤条件
- 实现选择区域的保存和加载
- 支持自定义颜色方案

### 集成可能
- 与其他分析组件联动
- 支持实时数据更新
- 添加机器学习预测功能

---

通过这个交互功能，您可以直观地探索患者数据，发现隐藏的模式，并进行深入的数据分析。祝您使用愉快！
