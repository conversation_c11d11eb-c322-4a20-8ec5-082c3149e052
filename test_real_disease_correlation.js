/**
 * 测试真实聚类数据的疾病关联分析
 * 验证Disease Correlation热力图使用真实数据而非Mock数据
 */

// 测试函数：验证真实疾病关联数据加载
async function testRealDiseaseCorrelation() {
  console.log('🧪 开始测试真实疾病关联数据加载...');
  
  try {
    // 1. 检查后端连接
    console.log('1️⃣ 检查后端连接...');
    const connectionStatus = await patientDataProcessor.checkBackendConnection();
    
    if (!connectionStatus.connected) {
      console.error('❌ 后端未连接');
      return false;
    }
    console.log('✅ 后端连接正常');
    
    // 2. 确保聚类数据已加载
    console.log('2️⃣ 检查聚类数据...');
    const clusteringResult = await patientDataProcessor.runFullPipeline({ useDefault: true });
    
    if (!clusteringResult || !clusteringResult.visualizationData) {
      console.error('❌ 聚类数据加载失败');
      return false;
    }
    
    console.log(`✅ 聚类数据加载成功: ${clusteringResult.visualizationData.length} 个患者`);
    
    // 3. 测试疾病关联API
    console.log('3️⃣ 测试疾病关联API...');
    
    const correlationData = await patientDataProcessor.getDiseaseCorrelation({
      topDiseases: 12
    });
    
    if (!correlationData) {
      console.error('❌ 疾病关联数据获取失败');
      return false;
    }
    
    console.log('✅ 疾病关联数据获取成功');
    console.log('📊 关联数据结构:', {
      hasCorrelationMatrix: !!correlationData.correlation_matrix,
      matrixSize: correlationData.correlation_matrix?.size,
      diseaseCount: correlationData.correlation_matrix?.diseases?.length,
      analysisMethod: correlationData.summary?.analysis_method
    });
    
    // 4. 验证数据内容
    console.log('4️⃣ 验证数据内容...');
    
    const matrix = correlationData.correlation_matrix;
    if (!matrix || !matrix.diseases || !matrix.matrix) {
      console.error('❌ 关联矩阵数据结构无效');
      return false;
    }
    
    console.log('📋 疾病列表:');
    matrix.diseases.forEach((disease, index) => {
      console.log(`   ${index + 1}. ${disease}`);
    });
    
    // 检查矩阵数据
    const matrixData = matrix.matrix;
    console.log(`📊 矩阵维度: ${matrixData.length} x ${matrixData[0]?.length}`);
    
    // 找出最强的关联关系
    let maxCorrelation = 0;
    let maxPair = null;
    
    for (let i = 0; i < matrix.diseases.length; i++) {
      for (let j = i + 1; j < matrix.diseases.length; j++) {
        const correlation = Math.max(matrixData[i][j], matrixData[j][i]);
        if (correlation > maxCorrelation && correlation < 1.0) { // 排除自相关
          maxCorrelation = correlation;
          maxPair = [matrix.diseases[i], matrix.diseases[j]];
        }
      }
    }
    
    if (maxPair) {
      console.log(`🔗 最强关联关系: ${maxPair[0]} ↔ ${maxPair[1]} (${maxCorrelation.toFixed(3)})`);
    }
    
    // 5. 测试前端组件数据更新
    console.log('5️⃣ 测试前端组件数据更新...');
    
    // 检查DiagnosisAssistant组件是否正确接收数据
    // 这需要在浏览器环境中运行
    if (typeof window !== 'undefined' && window.Vue) {
      console.log('🎭 检查Vue组件状态...');
      
      // 查找DiagnosisAssistant组件实例
      const app = document.querySelector('#app').__vue__;
      if (app && app.$children) {
        const diagnosisComponent = app.$children.find(child => 
          child.$options.name === 'DiagnosisAssistant' || 
          child.correlationMatrix !== undefined
        );
        
        if (diagnosisComponent) {
          console.log('📱 找到DiagnosisAssistant组件');
          console.log('   correlationMatrix:', diagnosisComponent.correlationMatrix);
          console.log('   diseaseCorrelationData:', diagnosisComponent.diseaseCorrelationData);
          console.log('   backendConnected:', diagnosisComponent.backendConnected);
          
          // 手动触发数据加载
          if (diagnosisComponent.loadDiseaseCorrelationData) {
            console.log('🔄 手动触发疾病关联数据加载...');
            await diagnosisComponent.loadDiseaseCorrelationData();
            
            console.log('📊 加载后的数据状态:');
            console.log('   correlationMatrix:', diagnosisComponent.correlationMatrix);
          }
        } else {
          console.log('⚠️ 未找到DiagnosisAssistant组件');
        }
      }
    }
    
    // 6. 验证热力图数据格式
    console.log('6️⃣ 验证热力图数据格式...');
    
    // 模拟生成热力图数据
    const heatmapData = [];
    for (let i = 0; i < matrix.diseases.length; i++) {
      for (let j = 0; j < matrix.diseases.length; j++) {
        heatmapData.push([i, j, matrixData[i][j]]);
      }
    }
    
    console.log('📊 热力图数据验证:');
    console.log(`   数据点数量: ${heatmapData.length}`);
    console.log(`   期望数量: ${matrix.diseases.length * matrix.diseases.length}`);
    console.log(`   数据完整: ${heatmapData.length === matrix.diseases.length * matrix.diseases.length ? '✅' : '❌'}`);
    
    // 显示数据范围
    const values = heatmapData.map(d => d[2]);
    console.log(`   数值范围: ${Math.min(...values).toFixed(3)} - ${Math.max(...values).toFixed(3)}`);
    
    // 显示部分数据样本
    console.log('📋 数据样本 (前5个):');
    heatmapData.slice(0, 5).forEach((point, index) => {
      const [x, y, value] = point;
      console.log(`   ${index + 1}. [${x}, ${y}] = ${value.toFixed(3)} (${matrix.diseases[x]} → ${matrix.diseases[y]})`);
    });
    
    console.log('🎉 真实疾病关联数据测试完成！');
    
    return {
      correlationData,
      matrix,
      heatmapData,
      testResults: {
        apiWorking: true,
        dataValid: true,
        matrixGenerated: true,
        realData: true
      }
    };
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    console.error('错误详情:', error.message);
    console.error('错误堆栈:', error.stack);
    return false;
  }
}

// 测试函数：强制刷新组件数据
async function forceRefreshCorrelationData() {
  console.log('🔄 强制刷新疾病关联数据...');
  
  try {
    // 直接调用API获取数据
    const correlationData = await patientDataProcessor.getDiseaseCorrelation({
      topDiseases: 15
    });
    
    console.log('📊 获取到的数据:', correlationData);
    
    // 如果在浏览器环境中，尝试更新组件
    if (typeof window !== 'undefined' && window.Vue) {
      const app = document.querySelector('#app').__vue__;
      if (app && app.$children) {
        const diagnosisComponent = app.$children.find(child => 
          child.correlationMatrix !== undefined || 
          child.loadDiseaseCorrelationData !== undefined
        );
        
        if (diagnosisComponent) {
          console.log('📱 更新组件数据...');
          
          // 直接设置数据
          diagnosisComponent.diseaseCorrelationData = correlationData;
          diagnosisComponent.correlationMatrix = correlationData.correlation_matrix;
          diagnosisComponent.associationRules = correlationData.association_rules || [];
          
          // 强制更新视图
          diagnosisComponent.$forceUpdate();
          
          console.log('✅ 组件数据已更新');
          console.log('📊 当前correlationMatrix:', diagnosisComponent.correlationMatrix);
          
          return true;
        }
      }
    }
    
    console.log('⚠️ 无法找到组件或不在浏览器环境中');
    return correlationData;
    
  } catch (error) {
    console.error('❌ 强制刷新失败:', error);
    return false;
  }
}

// 测试函数：检查热力图标题
function checkHeatmapTitle() {
  console.log('🔍 检查热力图标题...');
  
  if (typeof document !== 'undefined') {
    // 查找Disease Correlation面板
    const correlationPanel = document.querySelector('.panel-header');
    if (correlationPanel && correlationPanel.textContent.includes('Disease Correlation')) {
      console.log('📊 找到Disease Correlation面板');
      
      // 查找图表标题
      const chartTitles = document.querySelectorAll('.echarts-title, [class*="title"]');
      chartTitles.forEach((title, index) => {
        console.log(`   标题 ${index + 1}: ${title.textContent}`);
      });
      
      // 检查是否还显示Mock Data
      const mockDataElements = document.querySelectorAll('*');
      let foundMockData = false;
      
      mockDataElements.forEach(element => {
        if (element.textContent && element.textContent.includes('Mock Data')) {
          console.log(`⚠️ 仍然显示Mock Data: ${element.textContent}`);
          foundMockData = true;
        }
      });
      
      if (!foundMockData) {
        console.log('✅ 未发现Mock Data标识，可能已使用真实数据');
      }
      
      return !foundMockData;
    }
  }
  
  console.log('⚠️ 无法检查DOM元素');
  return false;
}

// 主测试函数
async function runRealDiseaseCorrelationTests() {
  console.log('🚀 开始运行真实疾病关联数据测试...');
  console.log('=' * 60);
  
  // 测试1: 真实数据加载
  const dataTest = await testRealDiseaseCorrelation();
  
  console.log('\n' + '=' * 60);
  
  // 测试2: 强制刷新数据
  console.log('🔄 强制刷新数据测试...');
  const refreshTest = await forceRefreshCorrelationData();
  
  console.log('\n' + '=' * 60);
  
  // 测试3: 检查界面显示
  console.log('🎭 检查界面显示...');
  const uiTest = checkHeatmapTitle();
  
  console.log('\n' + '=' * 60);
  console.log('📊 测试结果汇总:');
  console.log(`   真实数据加载: ${dataTest ? '✅ 通过' : '❌ 失败'}`);
  console.log(`   强制刷新数据: ${refreshTest ? '✅ 通过' : '❌ 失败'}`);
  console.log(`   界面显示检查: ${uiTest ? '✅ 通过' : '❌ 失败'}`);
  
  if (dataTest && refreshTest) {
    console.log('\n🎉 真实数据测试通过！');
    console.log('💡 现在Disease Correlation热力图应该显示:');
    console.log('   - 基于真实聚类数据的疾病关联关系');
    console.log('   - 使用共现频率计算的关联强度');
    console.log('   - 动态的疾病列表和关联矩阵');
    console.log('   - 标题不再显示"Mock Data"');
  } else {
    console.log('⚠️ 部分测试失败，请检查相关功能');
  }
  
  console.log('\n📋 故障排除建议:');
  console.log('   1. 确保后端服务正常运行');
  console.log('   2. 检查聚类数据是否已加载');
  console.log('   3. 查看浏览器控制台的错误信息');
  console.log('   4. 尝试刷新页面重新加载数据');
}

// 导出测试函数供浏览器控制台使用
if (typeof window !== 'undefined') {
  window.testRealDiseaseCorrelation = testRealDiseaseCorrelation;
  window.forceRefreshCorrelationData = forceRefreshCorrelationData;
  window.checkHeatmapTitle = checkHeatmapTitle;
  window.runRealDiseaseCorrelationTests = runRealDiseaseCorrelationTests;
  
  console.log('🧪 真实疾病关联数据测试函数已加载到全局作用域');
  console.log('💡 使用方法:');
  console.log('   - runRealDiseaseCorrelationTests() - 运行所有测试');
  console.log('   - testRealDiseaseCorrelation() - 测试数据加载');
  console.log('   - forceRefreshCorrelationData() - 强制刷新数据');
  console.log('   - checkHeatmapTitle() - 检查界面显示');
}

export { testRealDiseaseCorrelation, forceRefreshCorrelationData, checkHeatmapTitle, runRealDiseaseCorrelationTests };
