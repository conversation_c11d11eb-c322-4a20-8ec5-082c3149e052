import json
import os
from collections import defaultdict

# Path to the JSON file
file_path = os.path.join("Bibm-master", "Flask_end", "data", "Outpatient_Drug_Info.json")

# Function to prepare data for visualization
def prepare_med_disease_data():
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
        
        # Dictionary to store medication-diagnosis relationships
        med_diagnosis_map = defaultdict(lambda: defaultdict(int))
        med_details = {}  # Store medication details
        
        # Process all records
        for record in data:
            med_name = record.get("药品名称", "")
            med_code = record.get("药品代码", "")
            diag_code = record.get("主要诊断", "")
            diag_name = record.get("主要诊断名称", "")
            
            # Skip records without valid medication or diagnosis
            if not med_name or (not diag_code and not diag_name):
                continue
                
            # Use diagnosis name if code is missing
            diagnosis = diag_code if diag_code else diag_name
            diagnosis_display = diag_name if diag_name else diag_code
            
            # Count occurrence of this medication for this diagnosis
            med_diagnosis_map[med_name][diagnosis_display] += 1
            
            # Store medication details
            if med_name not in med_details:
                med_details[med_name] = {
                    "code": med_code,
                    "name": med_name,
                    "routes": set(),
                    "usage": set(),
                    "units": set()
                }
            
            # Add details for this medication
            med_details[med_name]["routes"].add(record.get("用药途径", ""))
            med_details[med_name]["usage"].add(record.get("用法", ""))
            med_details[med_name]["units"].add(record.get("用量单位", ""))
        
        # Convert sets to lists for JSON serialization
        for med in med_details:
            med_details[med]["routes"] = list(med_details[med]["routes"])
            med_details[med]["usage"] = list(med_details[med]["usage"])
            med_details[med]["units"] = list(med_details[med]["units"])
        
        # Prepare the final visualization data structure
        visualization_data = []
        
        for med_name, diagnoses in med_diagnosis_map.items():
            # Only include medications with at least 3 different diagnoses
            if len(diagnoses) >= 3:
                # Sort diagnoses by frequency
                sorted_diagnoses = sorted(diagnoses.items(), key=lambda x: x[1], reverse=True)
                
                # Take top 10 diagnoses for visualization
                top_diagnoses = sorted_diagnoses[:10]
                
                # Create radar chart data
                radar_data = {
                    "medication": med_name,
                    "medicationCode": med_details[med_name]["code"],
                    "diagnoses": [{"name": diag, "count": count} for diag, count in top_diagnoses],
                    "details": {
                        "routes": med_details[med_name]["routes"],
                        "usage": med_details[med_name]["usage"],
                        "units": med_details[med_name]["units"]
                    }
                }
                
                visualization_data.append(radar_data)
        
        # Sort by number of diagnoses (for medications with richer data)
        visualization_data.sort(key=lambda x: len(x["diagnoses"]), reverse=True)
        
        # Take top 30 medications with most diverse diagnoses
        visualization_data = visualization_data[:30]
        
        # Save the visualization data
        output_path = os.path.join("Bibm-master", "src", "assets", "medication_disease_data.json")
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as outfile:
            json.dump(visualization_data, outfile, ensure_ascii=False, indent=2)
            
        print(f"Visualization data created with {len(visualization_data)} medications")
        print(f"Data saved to: {output_path}")
        
        # Print sample of the first medication's data
        if visualization_data:
            print("\nSample data for first medication:")
            print(f"Medication: {visualization_data[0]['medication']}")
            print(f"Code: {visualization_data[0]['medicationCode']}")
            print("Top diagnoses:")
            for diag in visualization_data[0]['diagnoses'][:5]:
                print(f"  {diag['name']}: {diag['count']} occurrences")

if __name__ == "__main__":
    prepare_med_disease_data() 