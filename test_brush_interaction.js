/**
 * 测试矩形选择与季节分布交互功能
 * 验证当使用brush工具选择患者时，季节分布图会正确更新
 */

// 测试函数：验证brush选择交互
async function testBrushInteraction() {
  console.log('🧪 开始测试矩形选择与季节分布交互功能...');
  
  try {
    // 1. 检查后端连接状态
    console.log('1️⃣ 检查后端连接状态...');
    const connectionStatus = await patientDataProcessor.checkBackendConnection();
    
    if (!connectionStatus.connected) {
      console.error('❌ 后端未连接，无法进行测试');
      return false;
    }
    console.log('✅ 后端连接正常');
    
    // 2. 加载聚类数据
    console.log('2️⃣ 加载聚类数据...');
    const clusteringResult = await patientDataProcessor.runFullPipeline({ useDefault: true });
    
    if (!clusteringResult || !clusteringResult.visualizationData) {
      console.error('❌ 聚类数据加载失败');
      return false;
    }
    
    console.log(`✅ 聚类数据加载成功，包含 ${clusteringResult.visualizationData.length} 个患者`);
    
    // 3. 加载季节分布数据
    console.log('3️⃣ 加载季节分布数据...');
    const seasonalData = await patientDataProcessor.getSeasonalDistribution();
    
    if (!seasonalData || !seasonalData.series) {
      console.error('❌ 季节分布数据加载失败');
      return false;
    }
    
    console.log(`✅ 季节分布数据加载成功，包含 ${seasonalData.series.length} 个疾病系列`);
    console.log('📊 完整季节分布疾病:', seasonalData.series.map(s => s.name).slice(0, 5));
    
    // 4. 模拟brush选择事件
    console.log('4️⃣ 模拟brush选择事件...');
    
    // 模拟选择前10个患者
    const selectedPatients = clusteringResult.visualizationData.slice(0, 10);
    console.log(`📋 模拟选中 ${selectedPatients.length} 个患者`);
    
    // 显示选中患者的详细信息
    selectedPatients.forEach((patient, index) => {
      console.log(`   患者 ${index + 1}: ID=${patient.patientId}, 诊断=${patient.diagnosis || patient.main_diagnosis || '未知'}`);
    });
    
    // 5. 测试过滤逻辑
    console.log('5️⃣ 测试季节分布过滤逻辑...');
    
    // 提取选中患者的诊断
    const selectedDiagnoses = selectedPatients.map(patient => {
      return patient.diagnosis || patient.main_diagnosis || patient['主要诊断'];
    }).filter(diagnosis => diagnosis);
    
    const uniqueDiagnoses = [...new Set(selectedDiagnoses)];
    console.log(`🔍 选中患者的唯一诊断 (${uniqueDiagnoses.length} 种):`, uniqueDiagnoses);
    
    // 模拟DiagnosisAssistant的过滤方法
    const filterSeasonalDataByDiagnoses = (seasonalData, diagnoses) => {
      if (!seasonalData || !diagnoses || diagnoses.length === 0) {
        return null;
      }

      const diagnosisSet = new Set(diagnoses);
      const filteredData = {
        ...seasonalData,
        series: seasonalData.series.filter(series => 
          diagnosisSet.has(series.name)
        )
      };

      return filteredData;
    };
    
    const filteredSeasonalData = filterSeasonalDataByDiagnoses(seasonalData, uniqueDiagnoses);
    
    if (filteredSeasonalData && filteredSeasonalData.series.length > 0) {
      console.log(`✅ 过滤成功: ${seasonalData.series.length} → ${filteredSeasonalData.series.length} 个疾病系列`);
      console.log('📊 过滤后的疾病:', filteredSeasonalData.series.map(s => s.name));
    } else {
      console.log('⚠️ 过滤后无匹配疾病，将显示完整数据');
    }
    
    // 6. 测试清空选择
    console.log('6️⃣ 测试清空选择...');
    
    const emptyFilterResult = filterSeasonalDataByDiagnoses(seasonalData, []);
    if (emptyFilterResult === null) {
      console.log('✅ 清空选择正确，将显示完整数据');
    } else {
      console.log('❌ 清空选择逻辑有误');
    }
    
    // 7. 测试不同选择场景
    console.log('7️⃣ 测试不同选择场景...');
    
    // 场景1: 选择单个患者
    const singlePatient = [clusteringResult.visualizationData[0]];
    const singleDiagnosis = [singlePatient[0].diagnosis || singlePatient[0].main_diagnosis || '未知'];
    const singleResult = filterSeasonalDataByDiagnoses(seasonalData, singleDiagnosis);
    console.log(`📋 场景1 - 单个患者: ${singleResult ? singleResult.series.length : 0} 个疾病系列`);
    
    // 场景2: 选择大量患者
    const manyPatients = clusteringResult.visualizationData.slice(0, 50);
    const manyDiagnoses = [...new Set(manyPatients.map(p => p.diagnosis || p.main_diagnosis || '未知'))];
    const manyResult = filterSeasonalDataByDiagnoses(seasonalData, manyDiagnoses);
    console.log(`📋 场景2 - 多个患者: ${manyResult ? manyResult.series.length : 0} 个疾病系列`);
    
    // 场景3: 选择特定诊断的患者
    const specificDiagnosis = seasonalData.series[0].name; // 使用第一个疾病
    const specificPatients = clusteringResult.visualizationData.filter(p => 
      (p.diagnosis || p.main_diagnosis) === specificDiagnosis
    );
    console.log(`📋 场景3 - 特定诊断 "${specificDiagnosis}": 找到 ${specificPatients.length} 个患者`);
    
    if (specificPatients.length > 0) {
      const specificResult = filterSeasonalDataByDiagnoses(seasonalData, [specificDiagnosis]);
      console.log(`   过滤结果: ${specificResult ? specificResult.series.length : 0} 个疾病系列`);
    }
    
    // 8. 验证数据完整性
    console.log('8️⃣ 验证数据完整性...');
    
    // 检查患者数据结构
    const samplePatient = clusteringResult.visualizationData[0];
    const requiredFields = ['patientId', 'x', 'y'];
    const diagnosisFields = ['diagnosis', 'main_diagnosis', '主要诊断'];
    
    const hasRequiredFields = requiredFields.every(field => samplePatient.hasOwnProperty(field));
    const hasDiagnosisField = diagnosisFields.some(field => samplePatient.hasOwnProperty(field));
    
    console.log(`📋 患者数据结构检查:`);
    console.log(`   必需字段 (${requiredFields.join(', ')}): ${hasRequiredFields ? '✅' : '❌'}`);
    console.log(`   诊断字段 (${diagnosisFields.join(', ')}): ${hasDiagnosisField ? '✅' : '❌'}`);
    
    if (!hasRequiredFields || !hasDiagnosisField) {
      console.warn('⚠️ 患者数据结构可能不完整，可能影响交互功能');
    }
    
    // 9. 性能测试
    console.log('9️⃣ 性能测试...');
    
    const startTime = performance.now();
    
    // 模拟快速连续选择
    for (let i = 0; i < 20; i++) {
      const testPatients = clusteringResult.visualizationData.slice(i * 3, (i + 1) * 3);
      const testDiagnoses = testPatients.map(p => p.diagnosis || p.main_diagnosis || '未知');
      filterSeasonalDataByDiagnoses(seasonalData, testDiagnoses);
    }
    
    const endTime = performance.now();
    console.log(`⚡ 性能测试完成，20次过滤操作耗时: ${(endTime - startTime).toFixed(2)}ms`);
    
    console.log('🎉 矩形选择与季节分布交互功能测试完成！');
    return true;
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    return false;
  }
}

// 测试函数：模拟前端brush事件
function testBrushEventSimulation() {
  console.log('🎭 测试前端brush事件模拟...');
  
  // 模拟PatientClustering组件的brush事件处理
  const mockPatientClustering = {
    selectedPatients: [],
    chart: null,
    
    // 模拟handleBrushSelected方法
    handleBrushSelected(params) {
      console.log('🔄 模拟brush选择事件');
      
      if (params.batch && params.batch[0] && params.batch[0].selected && params.batch[0].selected.length > 0) {
        // 模拟获取选中的索引
        const selectedIndices = [];
        params.batch[0].selected.forEach(selection => {
          if (selection.dataIndex) {
            selectedIndices.push(...selection.dataIndex);
          }
        });
        
        if (selectedIndices.length > 0) {
          // 模拟获取选中的数据点
          const mockData = [
            { patientId: 'P001', diagnosis: 'H35.804', x: 1, y: 2 },
            { patientId: 'P002', diagnosis: 'H40.11', x: 2, y: 3 },
            { patientId: 'P003', diagnosis: 'H35.804', x: 3, y: 1 }
          ];
          
          const selectedData = selectedIndices.map(index => mockData[index % mockData.length]);
          
          this.selectedPatients = selectedData;
          console.log(`📋 选中 ${selectedData.length} 个患者`);
          
          // 模拟发出事件
          this.emitPatientsSelected(selectedData);
        }
      } else {
        // 清空选择
        this.selectedPatients = [];
        console.log('🔄 清空患者选择');
        this.emitPatientsSelected([]);
      }
    },
    
    // 模拟事件发出
    emitPatientsSelected(patients) {
      console.log(`📡 发出patients-selected事件: ${patients.length} 个患者`);
      
      // 模拟DiagnosisAssistant接收事件
      mockDiagnosisAssistant.handlePatientsSelected(patients);
    }
  };
  
  // 模拟DiagnosisAssistant组件
  const mockDiagnosisAssistant = {
    selectedPatients: [],
    realSeasonalData: {
      series: [
        { name: 'H35.804', data: [10, 15, 20, 12] },
        { name: 'H40.11', data: [8, 12, 18, 14] },
        { name: 'H25.9', data: [5, 8, 10, 7] }
      ]
    },
    filteredSeasonalData: null,
    
    // 模拟handlePatientsSelected方法
    handlePatientsSelected(selectedPatients) {
      console.log(`🎯 DiagnosisAssistant接收到 ${selectedPatients.length} 个选中患者`);
      this.selectedPatients = selectedPatients;
      this.filterSeasonalDataByPatients(selectedPatients);
    },
    
    // 模拟过滤方法
    filterSeasonalDataByPatients(selectedPatients) {
      if (!selectedPatients || selectedPatients.length === 0) {
        this.filteredSeasonalData = null;
        console.log('📊 显示完整季节分布数据');
        return;
      }
      
      const selectedDiagnoses = selectedPatients.map(p => p.diagnosis).filter(d => d);
      const uniqueDiagnoses = [...new Set(selectedDiagnoses)];
      
      console.log(`🔍 提取到 ${uniqueDiagnoses.length} 种唯一诊断:`, uniqueDiagnoses);
      
      const diagnosisSet = new Set(uniqueDiagnoses);
      this.filteredSeasonalData = {
        ...this.realSeasonalData,
        series: this.realSeasonalData.series.filter(series => 
          diagnosisSet.has(series.name)
        )
      };
      
      console.log(`📊 过滤后季节分布: ${this.filteredSeasonalData.series.length} 个疾病系列`);
    }
  };
  
  // 测试交互流程
  console.log('📋 测试完整交互流程...');
  
  // 1. 模拟brush选择事件
  console.log('1️⃣ 模拟brush选择');
  const mockBrushParams = {
    batch: [{
      selected: [{
        dataIndex: [0, 1, 2]
      }]
    }]
  };
  
  mockPatientClustering.handleBrushSelected(mockBrushParams);
  
  // 2. 模拟清空选择
  console.log('2️⃣ 模拟清空选择');
  const emptyBrushParams = {
    batch: []
  };
  
  mockPatientClustering.handleBrushSelected(emptyBrushParams);
  
  console.log('✅ 前端brush事件模拟测试完成');
  return true;
}

// 主测试函数
async function runBrushInteractionTests() {
  console.log('🚀 开始运行矩形选择交互测试...');
  console.log('=' * 60);
  
  // 测试1: 后端数据交互
  const backendTest = await testBrushInteraction();
  
  console.log('\n' + '=' * 60);
  
  // 测试2: 前端事件模拟
  const frontendTest = testBrushEventSimulation();
  
  console.log('\n' + '=' * 60);
  console.log('📊 测试结果汇总:');
  console.log(`   后端数据交互: ${backendTest ? '✅ 通过' : '❌ 失败'}`);
  console.log(`   前端事件模拟: ${frontendTest ? '✅ 通过' : '❌ 失败'}`);
  
  if (backendTest && frontendTest) {
    console.log('🎉 所有测试通过！矩形选择与季节分布交互功能正常');
    console.log('💡 使用说明:');
    console.log('   1. 初始状态: 季节分布显示完整数据');
    console.log('   2. 矩形选择: 在聚类图中拖拽选择患者');
    console.log('   3. 动态更新: 季节分布自动过滤显示选中患者的数据');
    console.log('   4. 清空选择: 使用清除工具恢复完整数据');
  } else {
    console.log('⚠️ 部分测试失败，请检查相关功能');
  }
}

// 导出测试函数供浏览器控制台使用
if (typeof window !== 'undefined') {
  window.testBrushInteraction = testBrushInteraction;
  window.testBrushEventSimulation = testBrushEventSimulation;
  window.runBrushInteractionTests = runBrushInteractionTests;
  
  console.log('🧪 矩形选择交互测试函数已加载到全局作用域');
  console.log('💡 使用方法:');
  console.log('   - runBrushInteractionTests() - 运行所有测试');
  console.log('   - testBrushInteraction() - 测试后端数据交互');
  console.log('   - testBrushEventSimulation() - 测试前端事件模拟');
}

export { testBrushInteraction, testBrushEventSimulation, runBrushInteractionTests };
