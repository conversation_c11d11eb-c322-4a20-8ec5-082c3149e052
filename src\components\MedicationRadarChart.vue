<template>
  <div class="medication-visualization-container">
    <!-- 图表容器 -->
    <div class="charts-container">
      <div v-show="displayMode === 'count'" ref="radarChartContainer" class="chart-container"></div>
      <div v-show="displayMode === 'trend'" ref="tagCloudContainer" class="chart-container"></div>
    </div>
    
    <!-- 筛选框移动到图表下方 -->
    <div class="chart-controls">
      <select v-model="selectedMedication" @change="updateChart">
        <option value="">选择药品</option>
        <option v-for="med in medicationData" :key="med.medication" :value="med.medication">
          {{ med.medication }}
        </option>
      </select>
    </div>
    
    <div v-if="selectedMedication" class="medication-info">
      <!-- 删除药品名称，只保留用途信息 -->
      <div class="medication-usage">
        <div><strong>用药途径:</strong> {{ currentMedication.details.routes.join(", ") }}</div>
        <div><strong>用法:</strong> {{ currentMedication.details.usage.join(", ") }}</div>
        <div><strong>单位:</strong> {{ currentMedication.details.units.join(", ") }}</div>
      </div>
    </div>
    
    <div v-if="selectedDiagnosis" class="diagnosis-details">
      <h4>{{ selectedDiagnosis.name }} 的用药详情</h4>
      <div class="detail-item">
        <div><strong>病例数:</strong> {{ selectedDiagnosis.count }}</div>
        <div><strong>用药途径:</strong> {{ currentMedication.details.routes.join(", ") }}</div>
        <div><strong>用法:</strong> {{ currentMedication.details.usage.join(", ") }}</div>
        <div><strong>单位:</strong> {{ currentMedication.details.units.join(", ") }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import 'echarts-wordcloud';
import medicationData from '../assets/medication_disease_data.json';

export default {
  name: 'MedicationRadarChart',
  props: {
    displayMode: {
      type: String,
      default: 'count', // 默认显示雷达图
      validator: (value) => ['count', 'trend'].includes(value)
    }
  },
  data() {
    return {
      medicationData: medicationData,
      selectedMedication: '',
      currentMedication: null,
      radarChart: null,
      tagCloud: null,
      selectedDiagnosis: null
    };
  },
  watch: {
    displayMode() {
      // 当显示模式变化时，确保相应的图表显示正确
      this.$nextTick(() => {
        if (this.radarChart) this.radarChart.resize();
        if (this.tagCloud) this.tagCloud.resize();
      });
    }
  },
  mounted() {
    console.log('MedicationRadarChart mounted');
    // Initialize with default medication if available
    if (this.medicationData.length > 0) {
      this.selectedMedication = this.medicationData[0].medication;
      this.updateChart();
    }
  },

  activated() {
    console.log('MedicationRadarChart activated');
    // 当组件被激活时，确保图表正确渲染
    this.$nextTick(() => {
      if (this.radarChart) {
        setTimeout(() => {
          this.radarChart.resize();
          console.log('MedicationRadarChart resized on activation');
        }, 100);
      }
      if (this.tagCloud) {
        setTimeout(() => {
          this.tagCloud.resize();
        }, 100);
      }
    });
  },

  beforeUnmount() {
    console.log('MedicationRadarChart beforeUnmount');
    if (this.radarChart) {
      this.radarChart.dispose();
      this.radarChart = null;
    }
    if (this.tagCloud) {
      this.tagCloud.dispose();
      this.tagCloud = null;
    }
  },
  methods: {
    updateChart() {
      // Find the selected medication data
      this.currentMedication = this.medicationData.find(med => med.medication === this.selectedMedication);
      
      if (this.currentMedication) {
        this.selectedDiagnosis = null; // Reset selected diagnosis
        this.renderRadarChart();
        this.renderTagCloud();
      }
    },
    
    renderRadarChart() {
      const chartContainer = this.$refs.radarChartContainer;

      if (!chartContainer) {
        console.warn('Radar chart container not found');
        return;
      }

      // Initialize chart if not already done
      if (!this.radarChart) {
        console.log('Initializing radar chart');
        this.radarChart = echarts.init(chartContainer);
      }
      
      // Get data for the radar chart
      const diagnoses = this.currentMedication.diagnoses;
      const maxCount = Math.max(...diagnoses.map(d => d.count));
      // 确保max值合理，避免ECharts警告
      const normalizedMax = Math.max(maxCount, 10); // 最小值为10，避免过小的max值

      const indicators = diagnoses.map(diag => ({
        name: diag.name,
        max: normalizedMax,
        min: 0
      }));
      
      const seriesData = [
        {
          value: diagnoses.map(diag => diag.count),
          name: '疾病分布',
          areaStyle: {
            opacity: 0.3,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(80, 141, 255, 0.5)' },
              { offset: 1, color: 'rgba(80, 141, 255, 0.1)' }
            ])
          },
          lineStyle: {
            width: 2,
            color: '#508dff'
          },
          itemStyle: {
            color: '#508dff'
          }
        }
      ];
      
      // Configure radar chart options
      const option = {
        title: {
        //   text: '药品-疾病使用频率分析',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          },
          left: 'center'
        },
        grid: {
          left: '10%',
          right: '10%',
          top: '10%',
          bottom: '10%',
          containLabel: true
        },
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            const dataIndex = params.dataIndex;
            const diagnosis = diagnoses[dataIndex];
            return `${diagnosis.name}<br/>病例数: ${diagnosis.count}`;
          }
        },
        radar: {
          indicator: indicators,
          shape: 'circle',
          splitNumber: Math.min(5, Math.max(3, Math.floor(normalizedMax / 10))),
          center: ['50%', '45%'], // 稍微上移，为下方控件留出空间
          radius: '55%', // 进一步减小雷达图半径
          axisName: {
            color: '#666',
            fontSize: 10, // 减小字体大小
            fontWeight: 'normal'
          },
          splitArea: {
            areaStyle: {
              color: ['rgba(255, 255, 255, 0.5)'],
              shadowColor: 'rgba(0, 0, 0, 0.2)',
              shadowBlur: 10
            }
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(238, 197, 102, 0.3)'
            }
          }
        },
        series: [
          {
            type: 'radar',
            emphasis: {
              lineStyle: {
                width: 4
              }
            },
            data: seriesData
          }
        ]
      };
      
      // Set chart options and add click event
      this.radarChart.setOption(option);
      
      // Add click event to radar chart
      this.radarChart.off('click');
      this.radarChart.on('click', (params) => {
        if (params.dataIndex !== undefined) {
          const diagnosisIndex = params.dataIndex;
          this.selectedDiagnosis = diagnoses[diagnosisIndex];
        }
      });
      
      // Resize chart on window resize
      window.addEventListener('resize', () => {
        if (this.radarChart) {
          this.radarChart.resize();
        }
      });
    },
    
    renderTagCloud() {
      const chartContainer = this.$refs.tagCloudContainer;
      
      if (!chartContainer) return;
      
      // Initialize chart if not already done
      if (!this.tagCloud) {
        this.tagCloud = echarts.init(chartContainer);
      }
      
      // Prepare data for the tag cloud
      const diagnoses = this.currentMedication.diagnoses;
      const tagCloudData = diagnoses.map(diag => ({
        name: diag.name,
        value: diag.count,
        textStyle: {
          fontWeight: diag.count > diagnoses[diagnoses.length - 1].count / 2 ? 'bold' : 'normal'
        }
      }));
      
      // Configure tag cloud options
      const option = {
        title: {
          text: '疾病关联分布',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          },
          left: 'center'
        },
        tooltip: {
          show: true,
          formatter: (params) => {
            return `${params.name}<br/>病例数: ${params.value}`;
          }
        },
        series: [{
          type: 'wordCloud',
          shape: 'circle',
          left: 'center',
          top: 'center',
          width: '90%',
          height: '90%',
          right: null,
          bottom: null,
          sizeRange: [12, 30],
          rotationRange: [-30, 30],
          rotationStep: 45,
          gridSize: 8,
          drawOutOfBound: false,
          layoutAnimation: true,
          textStyle: {
            fontFamily: 'sans-serif',
            fontWeight: 'bold',
            color: function() {
              // Random color
              return 'rgb(' + [
                Math.round(Math.random() * 160 + 40),
                Math.round(Math.random() * 160 + 40),
                Math.round(Math.random() * 160 + 40)
              ].join(',') + ')';
            }
          },
          emphasis: {
            textStyle: {
              shadowBlur: 10,
              shadowColor: '#333'
            }
          },
          data: tagCloudData
        }]
      };
      
      // Set chart options and add click event
      this.tagCloud.setOption(option);
      
      // Add click event to tag cloud
      this.tagCloud.off('click');
      this.tagCloud.on('click', (params) => {
        const diagnosis = diagnoses.find(d => d.name === params.name);
        if (diagnosis) {
          this.selectedDiagnosis = diagnosis;
        }
      });
      
      // Resize chart on window resize
      window.addEventListener('resize', () => {
        if (this.tagCloud) {
          this.tagCloud.resize();
        }
      });
    }
  }
};
</script>

<style scoped>
.medication-visualization-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
  height: 100%;
  padding: 10px;
  border-radius: 8px;
  background-color: #fff;
  box-sizing: border-box;
  overflow: visible; /* 允许内容溢出显示 */
}

.chart-controls {
  display: flex;
  justify-content: center;
  margin: 8px 0 5px 0; /* 增加上边距，减少下边距 */
}

select {
  padding: 6px 10px; /* 减少内边距 */
  border-radius: 4px;
  border: 1px solid #ccc;
  background-color: #f9f9f9;
  font-size: 13px; /* 减少字体大小 */
  min-width: 200px; /* 减少最小宽度 */
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.medication-info {
  display: flex;
  justify-content: center;
  margin: 3px 0; /* 减少边距 */
  padding-top: 3px; /* 减少内边距 */
  border-top: 1px solid #eee;
}

.medication-usage {
  display: flex;
  gap: 15px; /* 减少间距 */
  font-size: 12px; /* 减少字体大小 */
  flex-wrap: wrap;
  justify-content: center;
}

.charts-container {
  position: relative;
  width: 100%;
  height: 200px; /* 减少高度为下方控件留出空间 */
  flex: 0 0 200px; /* 固定高度，不伸缩 */
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: visible; /* 允许雷达图标签溢出显示 */
  padding: 10px; /* 减少内边距 */
  box-sizing: border-box;
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 180px; /* 减少最小高度 */
  overflow: visible; /* 允许雷达图标签溢出显示 */
}

.diagnosis-details {
  margin-top: 10px;
  padding: 15px;
  border-radius: 8px;
  background-color: #f9f9f9;
  border: 1px solid #eee;
}

.diagnosis-details h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #333;
}

.detail-item {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}
</style> 