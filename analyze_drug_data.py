import json
import os
from collections import defaultdict

# Path to the JSON file
file_path = os.path.join("Bibm-master", "Flask_end", "data", "Outpatient_Drug_Info.json")

# Analyze the first few records to understand structure
with open(file_path, 'r', encoding='utf-8') as f:
    data = json.load(f)
    
    # Print the number of records
    print(f"Total records: {len(data)}")
    
    # Print the keys from the first record
    if data:
        print("\nSample record keys:")
        for key in data[0].keys():
            print(f"  {key}")
    
    # Extract and count unique medications and diagnoses
    medications = defaultdict(int)
    diagnoses = defaultdict(int)
    
    # Limit to first 1000 records for quick analysis
    for record in data[:1000]:
        med_name = record.get("药品名称", "")
        med_code = record.get("药品代码", "")
        diag_code = record.get("主要诊断", "")
        diag_name = record.get("主要诊断名称", "")
        
        if med_name:
            medications[(med_name, med_code)] += 1
        if diag_code or diag_name:
            diagnoses[(diag_code, diag_name)] += 1
    
    # Print top 10 medications by frequency
    print("\nTop 10 medications:")
    for (med_name, med_code), count in sorted(medications.items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"  {med_name} (Code: {med_code}): {count} occurrences")
    
    # Print top 10 diagnoses by frequency
    print("\nTop 10 diagnoses:")
    for (diag_code, diag_name), count in sorted(diagnoses.items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"  {diag_name} ({diag_code}): {count} occurrences")
    
    # Print a complete sample record
    if data:
        print("\nComplete sample record:")
        sample = data[0]
        for key, value in sample.items():
            print(f"  {key}: {value}") 