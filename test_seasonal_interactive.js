/**
 * 测试季节分布交互式过滤功能
 * 验证当在聚类图中选择患者时，季节分布图会动态更新
 */

// 测试函数：验证季节分布交互功能
async function testSeasonalInteractiveFiltering() {
  console.log('🧪 开始测试季节分布交互式过滤功能...');
  
  try {
    // 1. 检查后端连接状态
    console.log('1️⃣ 检查后端连接状态...');
    const connectionStatus = await patientDataProcessor.checkBackendConnection();
    
    if (!connectionStatus.connected) {
      console.error('❌ 后端未连接，无法进行测试');
      return false;
    }
    console.log('✅ 后端连接正常');
    
    // 2. 加载完整的季节分布数据
    console.log('2️⃣ 加载完整的季节分布数据...');
    const fullSeasonalData = await patientDataProcessor.getSeasonalDistribution();
    console.log('📊 完整季节分布数据:', fullSeasonalData);
    
    if (!fullSeasonalData || !fullSeasonalData.series) {
      console.error('❌ 季节分布数据格式无效');
      return false;
    }
    
    console.log(`✅ 成功加载季节分布数据，包含 ${fullSeasonalData.series.length} 个疾病系列`);
    
    // 3. 加载聚类数据
    console.log('3️⃣ 加载聚类数据...');
    const clusteringResult = await patientDataProcessor.runFullPipeline({ useDefault: true });
    
    if (!clusteringResult || !clusteringResult.visualizationData) {
      console.error('❌ 聚类数据加载失败');
      return false;
    }
    
    console.log(`✅ 聚类数据加载成功，包含 ${clusteringResult.visualizationData.length} 个患者`);
    
    // 4. 模拟患者选择
    console.log('4️⃣ 模拟患者选择...');
    
    // 选择前10个患者作为测试
    const selectedPatients = clusteringResult.visualizationData.slice(0, 10);
    console.log(`📋 选中 ${selectedPatients.length} 个患者进行测试`);
    
    // 显示选中患者的诊断信息
    const selectedDiagnoses = selectedPatients.map(patient => {
      return patient.diagnosis || patient.main_diagnosis || patient['主要诊断'] || '未知';
    });
    
    console.log('🔍 选中患者的诊断:', [...new Set(selectedDiagnoses)]);
    
    // 5. 测试过滤逻辑
    console.log('5️⃣ 测试季节分布过滤逻辑...');
    
    // 模拟DiagnosisAssistant组件的过滤方法
    const filterSeasonalDataByDiagnoses = (seasonalData, diagnoses) => {
      if (!seasonalData || !diagnoses || diagnoses.length === 0) {
        return null;
      }

      // 创建诊断集合用于快速查找
      const diagnosisSet = new Set(diagnoses);

      // 过滤季节数据
      const filteredData = {
        ...seasonalData,
        series: seasonalData.series.filter(series => 
          diagnosisSet.has(series.name)
        )
      };

      return filteredData;
    };
    
    const filteredSeasonalData = filterSeasonalDataByDiagnoses(fullSeasonalData, selectedDiagnoses);
    
    if (filteredSeasonalData) {
      console.log(`✅ 过滤成功，从 ${fullSeasonalData.series.length} 个疾病系列过滤到 ${filteredSeasonalData.series.length} 个`);
      console.log('📊 过滤后的疾病:', filteredSeasonalData.series.map(s => s.name));
    } else {
      console.log('ℹ️ 没有匹配的疾病，将显示完整数据');
    }
    
    // 6. 测试图表选项生成
    console.log('6️⃣ 测试图表选项生成...');
    
    const generateSeasonalChartOption = (seasonalData, selectedCount = 0) => {
      const isQuarterView = true; // 假设使用季度视图
      
      let xAxisData = seasonalData.xAxis || ['Q1', 'Q2', 'Q3', 'Q4'];
      let seriesData = seasonalData.series || [];

      return {
        title: {
          text: selectedCount > 0 
            ? `季节分布 (选中 ${selectedCount} 个患者)`
            : '季节分布 (完整数据)',
          left: 'center',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          type: 'scroll',
          orient: 'horizontal',
          left: 'center',
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value'
        },
        series: seriesData.map((series, index) => ({
          ...series,
          type: 'bar',
          stack: 'total'
        }))
      };
    };
    
    // 生成完整数据的图表选项
    const fullChartOption = generateSeasonalChartOption(fullSeasonalData, 0);
    console.log('📈 完整数据图表选项生成成功');
    
    // 生成过滤数据的图表选项
    if (filteredSeasonalData) {
      const filteredChartOption = generateSeasonalChartOption(filteredSeasonalData, selectedPatients.length);
      console.log('📈 过滤数据图表选项生成成功');
    }
    
    // 7. 测试不同选择场景
    console.log('7️⃣ 测试不同选择场景...');
    
    // 场景1: 选择单个患者
    const singlePatient = [clusteringResult.visualizationData[0]];
    const singleDiagnosis = [singlePatient[0].diagnosis || singlePatient[0].main_diagnosis || '未知'];
    const singleFiltered = filterSeasonalDataByDiagnoses(fullSeasonalData, singleDiagnosis);
    
    console.log(`📋 场景1 - 单个患者: ${singleFiltered ? singleFiltered.series.length : 0} 个疾病系列`);
    
    // 场景2: 选择大量患者
    const manyPatients = clusteringResult.visualizationData.slice(0, 50);
    const manyDiagnoses = [...new Set(manyPatients.map(p => p.diagnosis || p.main_diagnosis || '未知'))];
    const manyFiltered = filterSeasonalDataByDiagnoses(fullSeasonalData, manyDiagnoses);
    
    console.log(`📋 场景2 - 多个患者: ${manyFiltered ? manyFiltered.series.length : 0} 个疾病系列`);
    
    // 场景3: 清空选择
    const emptyFiltered = filterSeasonalDataByDiagnoses(fullSeasonalData, []);
    console.log(`📋 场景3 - 清空选择: ${emptyFiltered ? '有过滤数据' : '显示完整数据'}`);
    
    // 8. 性能测试
    console.log('8️⃣ 性能测试...');
    
    const startTime = performance.now();
    
    // 模拟快速连续选择
    for (let i = 0; i < 10; i++) {
      const testPatients = clusteringResult.visualizationData.slice(i * 5, (i + 1) * 5);
      const testDiagnoses = testPatients.map(p => p.diagnosis || p.main_diagnosis || '未知');
      filterSeasonalDataByDiagnoses(fullSeasonalData, testDiagnoses);
    }
    
    const endTime = performance.now();
    console.log(`⚡ 性能测试完成，10次过滤操作耗时: ${(endTime - startTime).toFixed(2)}ms`);
    
    console.log('🎉 季节分布交互式过滤功能测试完成！');
    return true;
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    return false;
  }
}

// 测试函数：模拟前端组件交互
function testFrontendInteraction() {
  console.log('🎭 测试前端组件交互...');
  
  // 模拟DiagnosisAssistant组件的状态
  const mockDiagnosisAssistant = {
    selectedPatients: [],
    realSeasonalData: null,
    filteredSeasonalData: null,
    seasonalViewMode: 'quarter',
    
    // 模拟handlePatientsSelected方法
    handlePatientsSelected(selectedPatients) {
      console.log(`🔄 模拟患者选择事件: ${selectedPatients.length} 个患者`);
      this.selectedPatients = selectedPatients;
      this.filterSeasonalDataByPatients(selectedPatients);
    },
    
    // 模拟filterSeasonalDataByPatients方法
    filterSeasonalDataByPatients(selectedPatients) {
      if (!this.realSeasonalData || !selectedPatients || selectedPatients.length === 0) {
        this.filteredSeasonalData = null;
        console.log('📊 显示完整季节分布数据');
        return;
      }

      console.log('🔍 根据选中患者过滤季节分布数据...');
      
      const selectedDiagnoses = selectedPatients.map(patient => {
        return patient.diagnosis || patient.main_diagnosis || patient['主要诊断'];
      }).filter(diagnosis => diagnosis);

      this.filteredSeasonalData = this.filterSeasonalDataByDiagnoses(selectedDiagnoses);
      
      console.log(`✅ 已过滤季节分布数据，包含 ${selectedDiagnoses.length} 种诊断`);
    },
    
    // 模拟filterSeasonalDataByDiagnoses方法
    filterSeasonalDataByDiagnoses(diagnoses) {
      if (!this.realSeasonalData || !diagnoses || diagnoses.length === 0) {
        return null;
      }

      const diagnosisSet = new Set(diagnoses);

      const filteredData = {
        ...this.realSeasonalData,
        series: this.realSeasonalData.series.filter(series => 
          diagnosisSet.has(series.name)
        )
      };

      return filteredData;
    }
  };
  
  // 模拟季节数据
  mockDiagnosisAssistant.realSeasonalData = {
    xAxis: ['Q1', 'Q2', 'Q3', 'Q4'],
    series: [
      { name: 'H35.804', data: [10, 15, 20, 12] },
      { name: 'H40.11', data: [8, 12, 18, 14] },
      { name: 'H25.9', data: [5, 8, 10, 7] }
    ]
  };
  
  // 模拟患者数据
  const mockPatients = [
    { diagnosis: 'H35.804', patientId: 'P001' },
    { diagnosis: 'H40.11', patientId: 'P002' },
    { diagnosis: 'H35.804', patientId: 'P003' }
  ];
  
  // 测试交互
  console.log('📋 初始状态: 显示完整数据');
  
  console.log('🎯 模拟选择患者...');
  mockDiagnosisAssistant.handlePatientsSelected(mockPatients);
  
  console.log('🔄 模拟清空选择...');
  mockDiagnosisAssistant.handlePatientsSelected([]);
  
  console.log('✅ 前端组件交互测试完成');
  return true;
}

// 主测试函数
async function runSeasonalInteractiveTests() {
  console.log('🚀 开始运行季节分布交互式过滤测试...');
  console.log('=' * 60);
  
  // 测试1: 后端数据交互
  const backendTest = await testSeasonalInteractiveFiltering();
  
  console.log('\n' + '=' * 60);
  
  // 测试2: 前端组件交互
  const frontendTest = testFrontendInteraction();
  
  console.log('\n' + '=' * 60);
  console.log('📊 测试结果汇总:');
  console.log(`   后端数据交互: ${backendTest ? '✅ 通过' : '❌ 失败'}`);
  console.log(`   前端组件交互: ${frontendTest ? '✅ 通过' : '❌ 失败'}`);
  
  if (backendTest && frontendTest) {
    console.log('🎉 所有测试通过！季节分布交互式过滤功能正常');
    console.log('💡 功能说明:');
    console.log('   - 初始状态显示完整的季节分布数据');
    console.log('   - 在聚类图中使用矩形选择等工具选中患者');
    console.log('   - 季节分布图会自动过滤显示选中患者的数据');
    console.log('   - 清空选择时恢复显示完整数据');
  } else {
    console.log('⚠️ 部分测试失败，请检查相关功能');
  }
}

// 导出测试函数供浏览器控制台使用
if (typeof window !== 'undefined') {
  window.testSeasonalInteractiveFiltering = testSeasonalInteractiveFiltering;
  window.testFrontendInteraction = testFrontendInteraction;
  window.runSeasonalInteractiveTests = runSeasonalInteractiveTests;
  
  console.log('🧪 季节分布交互式过滤测试函数已加载到全局作用域');
  console.log('💡 使用方法:');
  console.log('   - runSeasonalInteractiveTests() - 运行所有测试');
  console.log('   - testSeasonalInteractiveFiltering() - 测试后端数据交互');
  console.log('   - testFrontendInteraction() - 测试前端组件交互');
}

export { testSeasonalInteractiveFiltering, testFrontendInteraction, runSeasonalInteractiveTests };
