# 疾病种类显示功能使用指南

## 功能概述

现在当您在聚类图中框选患者后，季节分布图不仅会显示选中患者的数量，还会显示选中患者包含的疾病种类信息，让您更好地了解所选患者群体的疾病构成。

## 🎯 新增功能特性

### 主标题显示
- **完整数据**: "Seasonal Distribution (Complete Data)"
- **选中患者**: "Seasonal Distribution (X patients selected)"

### 副标题显示疾病信息
- **疾病种类数量**: 显示选中患者包含多少种不同的疾病
- **疾病名称列表**: 显示具体的疾病名称（最多显示前3种）
- **智能截断**: 超过3种疾病时用"..."表示还有更多

### 显示格式示例
```
主标题: Seasonal Distribution (16 patients selected)
副标题: Disease Types: 3 (H35.804, H40.11, H25.9)
```

## 📊 不同场景的显示效果

### 场景1: 单一疾病类型
```
选中患者: 5个患者，都是黄斑水肿
显示效果:
  主标题: Seasonal Distribution (5 patients selected)
  副标题: Disease Types: 1 (H35.804)
```

### 场景2: 多种疾病类型
```
选中患者: 12个患者，包含3种疾病
显示效果:
  主标题: Seasonal Distribution (12 patients selected)
  副标题: Disease Types: 3 (H35.804, H40.11, H25.9)
```

### 场景3: 大量疾病类型
```
选中患者: 25个患者，包含6种疾病
显示效果:
  主标题: Seasonal Distribution (25 patients selected)
  副标题: Disease Types: 6 (H35.804, H40.11, H25.9...)
```

### 场景4: 完整数据
```
无选中患者: 显示所有数据
显示效果:
  主标题: Seasonal Distribution (Complete Data)
  副标题: 无
```

## 🎨 界面布局调整

### 自动布局适应
- **有副标题时**: 图例和图表区域自动下移，为副标题留出空间
- **无副标题时**: 使用标准布局，最大化图表显示区域

### 视觉层次
- **主标题**: 14px，正常字重，深色
- **副标题**: 12px，斜体，灰色，位置居中

## 🔧 技术实现细节

### 疾病信息提取
```javascript
// 从选中患者中提取诊断信息
const selectedDiagnoses = selectedPatients.map(patient => {
  return patient.diagnosis || 
         patient.main_diagnosis || 
         patient['主要诊断'] ||
         patient.category ||
         patient.diseaseId;
}).filter(diagnosis => diagnosis);

// 统计疾病分布
const diseaseCount = {};
selectedDiagnoses.forEach(disease => {
  diseaseCount[disease] = (diseaseCount[disease] || 0) + 1;
});

const uniqueDiseases = [...new Set(selectedDiagnoses)];
```

### 副标题生成
```javascript
subtitle: seasonalData.isFiltered && seasonalData.selectedDiseases ? {
  text: `Disease Types: ${seasonalData.selectedDiseases.length} (${seasonalData.selectedDiseases.slice(0, 3).join(', ')}${seasonalData.selectedDiseases.length > 3 ? '...' : ''})`,
  left: 'center',
  top: 25,
  textStyle: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic'
  }
} : undefined
```

### 布局自适应
```javascript
// 图例位置调整
top: seasonalData.isFiltered && seasonalData.selectedDiseases ? 45 : 30,

// 图表区域调整
top: seasonalData.isFiltered && seasonalData.selectedDiseases ? '25%' : '20%',
```

## 📋 使用步骤

### 1. 查看完整数据
- 页面加载后自动显示完整的季节分布
- 主标题显示 "Complete Data"
- 无副标题

### 2. 框选患者
- 在聚类图中使用矩形选择工具框选患者
- 主标题更新显示选中患者数量
- 副标题显示疾病种类信息

### 3. 分析疾病构成
- 通过副标题了解选中患者的疾病多样性
- 查看具体的疾病名称（前3种）
- 了解疾病种类总数

### 4. 对比分析
- 图表保持完整的疾病结构
- 数值按选中患者比例调整
- 可以直观对比选中群体与整体的差异

### 5. 清空选择
- 使用"清除选择"工具
- 恢复显示完整数据
- 副标题消失

## 💡 使用技巧

### 疾病分析技巧
1. **单一疾病研究**: 框选特定疾病的患者，观察该疾病的季节性模式
2. **疾病对比**: 分别框选不同疾病的患者，对比它们的季节分布差异
3. **复合疾病分析**: 框选包含多种疾病的患者群，了解疾病组合的季节特征
4. **样本大小影响**: 对比不同样本大小的选择，观察季节模式的稳定性

### 界面使用技巧
1. **快速识别**: 通过副标题快速了解选中患者的疾病构成
2. **信息完整性**: 即使显示"..."也能通过疾病总数了解完整情况
3. **视觉对比**: 利用保持一致的疾病结构进行直观对比
4. **上下文保持**: 始终能看到完整的疾病分类，不会丢失上下文

## 🔍 故障排除

### 常见问题

#### 1. 副标题不显示
**可能原因**: 选中的患者没有有效的诊断信息
**解决方案**: 
- 检查患者数据是否包含诊断字段
- 确认诊断字段不为空
- 查看浏览器控制台的调试信息

#### 2. 疾病名称显示异常
**可能原因**: 诊断代码格式不标准
**解决方案**:
- 检查诊断代码的格式
- 确认数据源的一致性
- 查看提取的诊断信息是否正确

#### 3. 布局显示问题
**可能原因**: 副标题过长或浏览器窗口过小
**解决方案**:
- 调整浏览器窗口大小
- 检查疾病名称长度
- 确认CSS样式正确应用

### 调试方法

#### 在浏览器控制台中运行测试
```javascript
// 运行完整测试
runDiseaseTypesDisplayTests();

// 单独测试功能
testDiseaseTypesDisplay();

// 测试用户体验
testUserExperienceWithDiseaseTypes();
```

#### 查看调试信息
框选患者后，在控制台中查看：
- 选中患者的诊断信息
- 疾病统计结果
- 生成的图表数据

## 🚀 未来扩展

### 计划功能
- **疾病详情弹窗**: 点击副标题显示完整的疾病列表和统计
- **疾病过滤**: 基于疾病类型进行快速过滤
- **疾病颜色映射**: 不同疾病使用不同颜色标识
- **疾病趋势分析**: 显示疾病随时间的变化趋势

### 自定义选项
- **显示数量控制**: 自定义副标题中显示的疾病数量
- **排序方式**: 按患者数量或字母顺序排序疾病
- **显示格式**: 自定义疾病名称的显示格式

## 📈 价值体现

### 对用户的价值
1. **信息丰富**: 不仅知道选了多少患者，还知道选了什么疾病的患者
2. **分析深入**: 可以基于疾病类型进行更精确的季节性分析
3. **决策支持**: 为临床研究和决策提供更详细的数据支持
4. **效率提升**: 快速了解患者群体特征，无需额外查询

### 对系统的价值
1. **功能完整**: 提供更全面的数据分析能力
2. **用户体验**: 提升界面的信息密度和实用性
3. **数据利用**: 更充分地利用现有的患者诊断数据
4. **扩展性**: 为后续功能扩展奠定基础

---

通过这个增强功能，您现在可以更深入地分析选中患者群体的疾病构成和季节性模式，为医疗数据分析提供更强大的工具支持！
