<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>季节分布过滤修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .header {
            text-align: center;
            color: #333;
            border-bottom: 2px solid #4682B4;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .status {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .status-item {
            flex: 1;
            text-align: center;
            padding: 10px;
            margin: 0 5px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .instructions {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #0066cc;
        }
        
        .step {
            margin: 10px 0;
            padding: 8px;
            background-color: #f8f9fa;
            border-left: 4px solid #4682B4;
            border-radius: 0 4px 4px 0;
        }
        
        .console-output {
            background-color: #1e1e1e;
            color: #ffffff;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .button {
            background-color: #4682B4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .button:hover {
            background-color: #5F9EA0;
        }
        
        .button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .debug-info h4 {
            margin-top: 0;
            color: #495057;
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-bar {
            height: 100%;
            background-color: #4682B4;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 季节分布过滤修复验证</h1>
            <p>验证框选患者后季节分布图不再显示空白的修复效果</p>
        </div>
        
        <div class="status">
            <div class="status-item status-warning" id="backend-status">
                🔄 检查后端连接...
            </div>
            <div class="status-item status-warning" id="data-status">
                🔄 检查数据加载...
            </div>
            <div class="status-item status-warning" id="filter-status">
                🔄 检查过滤逻辑...
            </div>
        </div>
        
        <div class="instructions">
            <h3>📋 测试步骤</h3>
            <div class="step">
                <strong>步骤 1:</strong> 点击"开始诊断"按钮检查系统状态
            </div>
            <div class="step">
                <strong>步骤 2:</strong> 在主页面的聚类图中使用矩形选择工具框选患者
            </div>
            <div class="step">
                <strong>步骤 3:</strong> 观察季节分布图是否正确更新（不再空白）
            </div>
            <div class="step">
                <strong>步骤 4:</strong> 使用"清除选择"工具验证是否恢复完整数据
            </div>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button class="button" onclick="startDiagnosis()">🔍 开始诊断</button>
            <button class="button" onclick="runQuickTest()">⚡ 快速测试</button>
            <button class="button" onclick="clearConsole()">🗑️ 清空日志</button>
        </div>
        
        <div class="progress" id="progress-container" style="display: none;">
            <div class="progress-bar" id="progress-bar"></div>
        </div>
        
        <div class="debug-info">
            <h4>🔍 诊断结果</h4>
            <div id="diagnosis-result">点击"开始诊断"按钮开始检查...</div>
        </div>
        
        <div class="debug-info">
            <h4>📊 控制台输出</h4>
            <div class="console-output" id="console-output">
                等待诊断开始...
            </div>
        </div>
    </div>

    <script>
        let consoleOutput = document.getElementById('console-output');
        let diagnosisResult = document.getElementById('diagnosis-result');
        
        // 重写console.log以显示在页面上
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            const timestamp = new Date().toLocaleTimeString();
            consoleOutput.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        };
        
        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.className = `status-item status-${status}`;
            element.innerHTML = message;
        }
        
        function updateProgress(percentage) {
            const progressContainer = document.getElementById('progress-container');
            const progressBar = document.getElementById('progress-bar');
            
            if (percentage > 0) {
                progressContainer.style.display = 'block';
                progressBar.style.width = percentage + '%';
            } else {
                progressContainer.style.display = 'none';
            }
        }
        
        function clearConsole() {
            consoleOutput.innerHTML = '控制台已清空...';
        }
        
        async function startDiagnosis() {
            console.log('🚀 开始系统诊断...');
            diagnosisResult.innerHTML = '正在诊断中...';
            
            try {
                updateProgress(10);
                
                // 检查后端连接
                console.log('1️⃣ 检查后端连接...');
                updateStatus('backend-status', 'warning', '🔄 检查中...');
                
                if (typeof patientDataProcessor === 'undefined') {
                    throw new Error('patientDataProcessor 未定义，请确保在主页面运行此测试');
                }
                
                const connectionStatus = await patientDataProcessor.checkBackendConnection();
                updateProgress(30);
                
                if (connectionStatus.connected) {
                    updateStatus('backend-status', 'success', '✅ 后端连接正常');
                    console.log('✅ 后端连接正常');
                } else {
                    updateStatus('backend-status', 'error', '❌ 后端连接失败');
                    throw new Error('后端连接失败');
                }
                
                // 检查数据加载
                console.log('2️⃣ 检查数据加载...');
                updateStatus('data-status', 'warning', '🔄 检查中...');
                
                const clusteringResult = await patientDataProcessor.runFullPipeline({ useDefault: true });
                const seasonalData = await patientDataProcessor.getSeasonalDistribution();
                updateProgress(60);
                
                if (clusteringResult && seasonalData) {
                    updateStatus('data-status', 'success', '✅ 数据加载正常');
                    console.log(`✅ 聚类数据: ${clusteringResult.visualizationData.length} 个患者`);
                    console.log(`✅ 季节数据: ${seasonalData.series.length} 个疾病系列`);
                } else {
                    updateStatus('data-status', 'error', '❌ 数据加载失败');
                    throw new Error('数据加载失败');
                }
                
                // 检查过滤逻辑
                console.log('3️⃣ 检查过滤逻辑...');
                updateStatus('filter-status', 'warning', '🔄 检查中...');
                
                // 运行调试函数
                if (typeof runSeasonalFilterDebug !== 'undefined') {
                    await runSeasonalFilterDebug();
                    updateStatus('filter-status', 'success', '✅ 过滤逻辑正常');
                } else {
                    console.log('⚠️ 调试函数未加载，跳过详细检查');
                    updateStatus('filter-status', 'warning', '⚠️ 部分检查跳过');
                }
                
                updateProgress(100);
                
                diagnosisResult.innerHTML = `
                    <div style="color: #155724;">
                        <h4>✅ 诊断完成</h4>
                        <p><strong>系统状态:</strong> 正常</p>
                        <p><strong>修复状态:</strong> 已应用智能匹配和备用方案</p>
                        <p><strong>建议:</strong> 现在可以在主页面测试框选功能</p>
                    </div>
                `;
                
                setTimeout(() => updateProgress(0), 2000);
                
            } catch (error) {
                console.error('❌ 诊断失败:', error);
                diagnosisResult.innerHTML = `
                    <div style="color: #721c24;">
                        <h4>❌ 诊断失败</h4>
                        <p><strong>错误:</strong> ${error.message}</p>
                        <p><strong>建议:</strong> 请确保在主页面运行此测试，并检查后端服务状态</p>
                    </div>
                `;
                updateProgress(0);
            }
        }
        
        async function runQuickTest() {
            console.log('⚡ 运行快速测试...');
            
            try {
                // 模拟患者选择和过滤
                const mockPatients = [
                    { patientId: 'P001', diagnosis: 'H35.804', cluster: 0 },
                    { patientId: 'P002', diagnosis: 'H40.11', cluster: 1 },
                    { patientId: 'P003', diagnosis: 'H25.9', cluster: 2 }
                ];
                
                console.log('🧪 模拟患者选择:', mockPatients);
                
                // 模拟过滤逻辑
                const diagnoses = mockPatients.map(p => p.diagnosis);
                console.log('🔍 提取的诊断:', diagnoses);
                
                // 模拟季节数据
                const mockSeasonalData = {
                    series: [
                        { name: 'H35.804', data: [10, 15, 20, 12] },
                        { name: 'H40.11', data: [8, 12, 18, 14] },
                        { name: 'H25.9', data: [5, 8, 10, 7] }
                    ]
                };
                
                const diagnosisSet = new Set(diagnoses);
                const filteredSeries = mockSeasonalData.series.filter(series => 
                    diagnosisSet.has(series.name)
                );
                
                console.log('📊 过滤结果:', filteredSeries);
                
                if (filteredSeries.length > 0) {
                    console.log('✅ 快速测试通过：过滤逻辑正常工作');
                    diagnosisResult.innerHTML = `
                        <div style="color: #155724;">
                            <h4>✅ 快速测试通过</h4>
                            <p>过滤逻辑正常工作，匹配到 ${filteredSeries.length} 个疾病系列</p>
                        </div>
                    `;
                } else {
                    console.log('❌ 快速测试失败：没有匹配的系列');
                    diagnosisResult.innerHTML = `
                        <div style="color: #721c24;">
                            <h4>❌ 快速测试失败</h4>
                            <p>过滤逻辑可能存在问题</p>
                        </div>
                    `;
                }
                
            } catch (error) {
                console.error('❌ 快速测试失败:', error);
            }
        }
        
        // 页面加载时的初始化
        window.addEventListener('load', function() {
            console.log('📄 修复验证页面已加载');
            console.log('💡 请在主页面打开此页面以进行完整测试');
            
            // 检查是否在主页面环境中
            if (typeof patientDataProcessor === 'undefined') {
                diagnosisResult.innerHTML = `
                    <div style="color: #856404;">
                        <h4>⚠️ 环境检查</h4>
                        <p>请在主页面的浏览器控制台中运行以下代码来打开此测试页面：</p>
                        <code style="background: #f8f9fa; padding: 5px; border-radius: 3px;">
                            window.open('test_fix_verification.html', '_blank');
                        </code>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
