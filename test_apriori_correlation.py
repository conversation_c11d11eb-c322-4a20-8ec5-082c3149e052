#!/usr/bin/env python3
"""
测试Apriori疾病关联分析
分析主要诊断和次要诊断之间的相关性
"""

import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
from mlxtend.frequent_patterns import apriori
from mlxtend.frequent_patterns import association_rules

def load_data(file_path):
    """加载患者数据"""
    print(f"加载数据文件: {file_path}")
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    print(f"加载了 {len(data)} 条患者记录")
    return data

def prepare_transactions(data):
    """准备事务数据，每个事务是一个患者的所有诊断代码集合"""
    transactions = []
    main_diagnoses = set()
    secondary_diagnoses = set()
    
    print("准备事务数据...")
    for patient in data:
        # 提取主要诊断
        main_diagnosis = patient.get('主要诊断') or patient.get('主要诊断名称')
        
        # 提取次要诊断
        secondary_diagnoses_list = []
        secondary_codes = patient.get('次要诊断代码', '')
        
        # 处理次要诊断代码
        if secondary_codes:
            if isinstance(secondary_codes, str):
                secondary_diagnoses_list.extend([d.strip() for d in secondary_codes.split(',') if d.strip()])
            elif isinstance(secondary_codes, list):
                secondary_diagnoses_list.extend([str(d).strip() for d in secondary_codes if str(d).strip()])
            else:
                secondary_diagnoses_list.append(str(secondary_codes))
        
        # 创建事务（疾病集合）
        transaction = set()
        
        if main_diagnosis:
            transaction.add(main_diagnosis)
            main_diagnoses.add(main_diagnosis)
        
        for secondary in secondary_diagnoses_list:
            if secondary and secondary != main_diagnosis:  # 避免重复
                transaction.add(secondary)
                secondary_diagnoses.add(secondary)
        
        # 只保留包含至少一个疾病的事务
        if len(transaction) > 0:
            transactions.append(transaction)
    
    print(f"准备了 {len(transactions)} 个事务")
    print(f"主要诊断种类: {len(main_diagnoses)}")
    print(f"次要诊断种类: {len(secondary_diagnoses)}")
    
    return transactions, main_diagnoses, secondary_diagnoses

def create_one_hot_encoding(transactions):
    """创建诊断代码的one-hot编码"""
    # 获取所有唯一的诊断代码
    all_diseases = set()
    for transaction in transactions:
        all_diseases.update(transaction)
    
    print(f"总共有 {len(all_diseases)} 种不同的诊断")
    
    # 创建one-hot编码DataFrame
    one_hot = pd.DataFrame(index=range(len(transactions)), columns=list(all_diseases))
    one_hot = one_hot.fillna(0)
    
    # 填充one-hot编码
    for i, transaction in enumerate(transactions):
        for disease in transaction:
            one_hot.at[i, disease] = 1
    
    return one_hot

def run_apriori_analysis(one_hot, min_support=0.01, min_confidence=0.1, min_lift=1.0):
    """运行Apriori分析并生成关联规则"""
    print(f"使用最小支持度 {min_support} 挖掘频繁项集...")
    frequent_itemsets = apriori(one_hot, min_support=min_support, use_colnames=True)
    print(f"找到 {len(frequent_itemsets)} 个频繁项集")
    
    print(f"使用最小置信度 {min_confidence} 和最小提升度 {min_lift} 生成关联规则...")
    rules = association_rules(frequent_itemsets, 
                             metric="confidence", 
                             min_threshold=min_confidence)
    
    # 筛选满足最小提升度的规则
    rules = rules[rules['lift'] >= min_lift]
    print(f"生成了 {len(rules)} 条关联规则")
    
    return frequent_itemsets, rules

def filter_main_to_secondary_rules(rules, main_diagnoses, secondary_diagnoses):
    """筛选主要诊断→次要诊断的规则"""
    # 筛选只包含单个前件和单个后件的规则
    single_rules = rules[rules['antecedents'].apply(lambda x: len(x) == 1) & 
                        rules['consequents'].apply(lambda x: len(x) == 1)]
    
    # 转换frozenset为列表
    single_rules['antecedent'] = single_rules['antecedents'].apply(lambda x: list(x)[0])
    single_rules['consequent'] = single_rules['consequents'].apply(lambda x: list(x)[0])
    
    # 筛选主要诊断→次要诊断的规则
    main_to_secondary_rules = single_rules[single_rules['antecedent'].isin(main_diagnoses) & 
                                         single_rules['consequent'].isin(secondary_diagnoses)]
    
    print(f"筛选出 {len(main_to_secondary_rules)} 条主要诊断→次要诊断的关联规则")
    return main_to_secondary_rules

def create_correlation_matrix(rules, main_diagnoses, secondary_diagnoses, top_n=15):
    """创建相关性矩阵"""
    # 获取最常见的主要诊断和次要诊断
    main_diagnosis_counter = Counter(rules['antecedent'])
    secondary_diagnosis_counter = Counter(rules['consequent'])
    
    top_main_diagnoses = [d for d, _ in main_diagnosis_counter.most_common(top_n)]
    top_secondary_diagnoses = [d for d, _ in secondary_diagnosis_counter.most_common(top_n)]
    
    # 创建相关性矩阵
    matrix = pd.DataFrame(index=top_main_diagnoses, columns=top_secondary_diagnoses)
    matrix = matrix.fillna(0.0)
    
    # 填充相关性矩阵
    for _, rule in rules.iterrows():
        main_diagnosis = rule['antecedent']
        secondary_diagnosis = rule['consequent']
        
        if main_diagnosis in top_main_diagnoses and secondary_diagnosis in top_secondary_diagnoses:
            matrix.at[main_diagnosis, secondary_diagnosis] = rule['lift']
    
    return matrix, top_main_diagnoses, top_secondary_diagnoses

def plot_heatmap(matrix, output_file='disease_correlation_heatmap.png'):
    """绘制相关性热力图"""
    plt.figure(figsize=(12, 10))
    
    # 绘制热力图
    ax = sns.heatmap(matrix, annot=True, fmt='.1f', cmap='Blues',
                    linewidths=0.5, cbar_kws={'label': 'Lift (提升度)'})
    
    plt.title('Disease Correlation (主要诊断 → 次要诊断)', fontsize=16)
    plt.ylabel('主要诊断', fontsize=14)
    plt.xlabel('次要诊断', fontsize=14)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图像
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"热力图已保存到 {output_file}")
    
    # 显示图像
    plt.show()

def main():
    # 设置参数
    min_support = 0.01
    min_confidence = 0.1
    min_lift = 1.0
    top_n = 15
    
    # 加载数据
    try:
        data = load_data('Bibm-master/Flask_end/data/patient_records.json')
    except FileNotFoundError:
        try:
            data = load_data('Flask_end/data/patient_records.json')
        except FileNotFoundError:
            print("无法找到数据文件，请确保路径正确")
            return
    
    # 准备事务数据
    transactions, main_diagnoses, secondary_diagnoses = prepare_transactions(data)
    
    # 创建one-hot编码
    one_hot = create_one_hot_encoding(transactions)
    
    # 运行Apriori分析
    frequent_itemsets, rules = run_apriori_analysis(one_hot, min_support, min_confidence, min_lift)
    
    # 筛选主要诊断→次要诊断的规则
    main_to_secondary_rules = filter_main_to_secondary_rules(rules, main_diagnoses, secondary_diagnoses)
    
    # 创建相关性矩阵
    matrix, top_main_diagnoses, top_secondary_diagnoses = create_correlation_matrix(
        main_to_secondary_rules, main_diagnoses, secondary_diagnoses, top_n)
    
    # 绘制热力图
    plot_heatmap(matrix)
    
    # 打印前10条规则
    print("\n前10条主要诊断→次要诊断的关联规则（按提升度排序）:")
    sorted_rules = main_to_secondary_rules.sort_values('lift', ascending=False).head(10)
    for i, (_, rule) in enumerate(sorted_rules.iterrows()):
        print(f"{i+1}. {rule['antecedent']} → {rule['consequent']}")
        print(f"   支持度: {rule['support']:.4f}, 置信度: {rule['confidence']:.4f}, 提升度: {rule['lift']:.4f}")

if __name__ == '__main__':
    main() 