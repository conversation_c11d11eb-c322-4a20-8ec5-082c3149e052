/**
 * 测试主要诊断代码同步功能
 * 验证Case Selection与聚类数据中的"主要诊断"字段保持同步
 */

// 测试函数：验证主要诊断代码同步
async function testDiagnosisCodeSync() {
  console.log('🧪 开始测试主要诊断代码同步功能...');
  
  try {
    // 1. 检查后端连接状态
    console.log('1️⃣ 检查后端连接状态...');
    const connectionStatus = await patientDataProcessor.checkBackendConnection();
    
    if (!connectionStatus.connected) {
      console.error('❌ 后端未连接，无法进行测试');
      return false;
    }
    console.log('✅ 后端连接正常');
    
    // 2. 获取当前诊断代码列表
    console.log('2️⃣ 获取当前主要诊断代码列表...');
    const currentDiagnoses = await patientDataProcessor.getDiagnosisList();
    console.log(`📋 当前诊断代码数量: ${currentDiagnoses.diagnoses.length}`);
    console.log('📋 前5个诊断代码:', currentDiagnoses.diagnoses.slice(0, 5).map(d => d.name));
    
    // 验证这些是诊断代码而不是诊断名称
    const sampleCodes = currentDiagnoses.diagnoses.slice(0, 5).map(d => d.name);
    const hasCodePattern = sampleCodes.some(code => /^[A-Z]\d+/.test(code)); // 检查是否有ICD代码格式
    
    if (hasCodePattern) {
      console.log('✅ 确认获取的是主要诊断代码（如 H35.804）');
    } else {
      console.log('⚠️ 获取的可能是诊断名称而不是代码');
    }
    
    // 3. 直接检查原始数据中的字段
    console.log('3️⃣ 检查原始数据中的主要诊断字段...');
    
    // 模拟检查原始数据
    const sampleDataCheck = {
      '主要诊断': 'H35.804',
      '主要诊断名称': '黄斑水肿',
      '次要诊断代码': 'H34.802',
      '次要诊断名称': '视网膜静脉阻塞'
    };
    
    console.log('📊 原始数据字段示例:');
    console.log(`   主要诊断: ${sampleDataCheck['主要诊断']}`);
    console.log(`   主要诊断名称: ${sampleDataCheck['主要诊断名称']}`);
    console.log(`   次要诊断代码: ${sampleDataCheck['次要诊断代码']}`);
    console.log(`   次要诊断名称: ${sampleDataCheck['次要诊断名称']}`);
    
    // 4. 测试聚类数据加载
    console.log('4️⃣ 测试聚类数据加载...');
    const clusteringResult = await patientDataProcessor.runFullPipeline({ useDefault: true });
    
    if (clusteringResult && clusteringResult.visualizationData) {
      console.log(`✅ 聚类数据加载成功，包含 ${clusteringResult.visualizationData.length} 个数据点`);
      console.log(`📊 聚类统计: ${clusteringResult.clusterStats.length} 个聚类`);
    } else {
      console.error('❌ 聚类数据加载失败');
      return false;
    }
    
    // 5. 重新获取诊断代码列表，检查同步状态
    console.log('5️⃣ 重新获取诊断代码列表，检查同步状态...');
    const updatedDiagnoses = await patientDataProcessor.getDiagnosisList();
    
    // 比较诊断代码列表
    const currentCodes = currentDiagnoses.diagnoses.map(d => d.name).sort();
    const updatedCodes = updatedDiagnoses.diagnoses.map(d => d.name).sort();
    
    const codesChanged = JSON.stringify(currentCodes) !== JSON.stringify(updatedCodes);
    
    if (codesChanged) {
      console.log('🔄 检测到诊断代码列表变化');
      console.log(`📈 代码数量变化: ${currentDiagnoses.diagnoses.length} → ${updatedDiagnoses.diagnoses.length}`);
      
      // 找出新增和删除的代码
      const addedCodes = updatedCodes.filter(d => !currentCodes.includes(d));
      const removedCodes = currentCodes.filter(d => !updatedCodes.includes(d));
      
      if (addedCodes.length > 0) {
        console.log('➕ 新增诊断代码:', addedCodes.slice(0, 5));
      }
      if (removedCodes.length > 0) {
        console.log('➖ 删除诊断代码:', removedCodes.slice(0, 5));
      }
    } else {
      console.log('✅ 诊断代码列表无变化，数据一致');
    }
    
    // 6. 验证代码格式
    console.log('6️⃣ 验证诊断代码格式...');
    const allCodes = updatedDiagnoses.diagnoses.map(d => d.name);
    
    // 统计不同格式的代码
    const icdCodes = allCodes.filter(code => /^[A-Z]\d+/.test(code));
    const chineseCodes = allCodes.filter(code => /[\u4e00-\u9fa5]/.test(code));
    const otherCodes = allCodes.filter(code => !(/^[A-Z]\d+/.test(code) || /[\u4e00-\u9fa5]/.test(code)));
    
    console.log(`📊 代码格式统计:`);
    console.log(`   ICD格式代码 (如 H35.804): ${icdCodes.length} 个`);
    console.log(`   中文名称 (如 黄斑水肿): ${chineseCodes.length} 个`);
    console.log(`   其他格式: ${otherCodes.length} 个`);
    
    if (icdCodes.length > chineseCodes.length) {
      console.log('✅ 主要使用诊断代码格式，符合预期');
    } else {
      console.log('⚠️ 主要使用中文名称，可能需要调整');
    }
    
    // 7. 显示示例代码
    console.log('7️⃣ 示例诊断代码:');
    const exampleCodes = allCodes.slice(0, 10);
    exampleCodes.forEach((code, index) => {
      const format = /^[A-Z]\d+/.test(code) ? '(ICD代码)' : 
                   /[\u4e00-\u9fa5]/.test(code) ? '(中文名称)' : '(其他格式)';
      console.log(`   ${index + 1}. ${code} ${format}`);
    });
    
    console.log('🎉 主要诊断代码同步测试完成！');
    return true;
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    return false;
  }
}

// 测试函数：模拟前端Case Selection同步
function testCaseSelectionSync() {
  console.log('🎭 测试Case Selection主要诊断代码同步...');
  
  // 模拟DiagnosisAssistant组件的同步方法
  const mockSyncDiagnosisCodesWithClusteringData = async () => {
    try {
      console.log('🔄 模拟同步主要诊断代码与聚类数据...');
      
      // 重新获取最新的诊断代码列表
      const diagnosisData = await patientDataProcessor.getDiagnosisList();
      const newCodes = diagnosisData.diagnoses.map(d => d.name);
      
      console.log(`📋 获取到 ${newCodes.length} 个诊断代码`);
      console.log('📋 前5个代码:', newCodes.slice(0, 5));
      
      // 模拟检查变化
      const mockCurrentCodes = ['H35.804', 'H40.11', 'H25.9'];
      const codesChanged = JSON.stringify(mockCurrentCodes) !== JSON.stringify(newCodes.slice(0, 3));
      
      if (codesChanged) {
        console.log('✅ 检测到诊断代码列表变化，模拟更新Case Selection...');
        console.log('🔄 Disease 1 代码已更新');
        console.log('🔄 Disease 2 代码已更新');
        console.log('✅ Case Selection与聚类数据（主要诊断代码）同步完成');
      } else {
        console.log('ℹ️ 诊断代码列表无变化，无需同步');
      }
      
      return true;
    } catch (error) {
      console.error('❌ 前端同步模拟失败:', error);
      return false;
    }
  };
  
  // 执行模拟同步
  return mockSyncDiagnosisCodesWithClusteringData();
}

// 主测试函数
async function runDiagnosisCodeTests() {
  console.log('🚀 开始运行主要诊断代码同步测试...');
  console.log('=' * 60);
  
  // 测试1: 后端诊断代码同步
  const backendTest = await testDiagnosisCodeSync();
  
  console.log('\n' + '=' * 60);
  
  // 测试2: 前端Case Selection同步
  const frontendTest = await testCaseSelectionSync();
  
  console.log('\n' + '=' * 60);
  console.log('📊 测试结果汇总:');
  console.log(`   后端诊断代码同步: ${backendTest ? '✅ 通过' : '❌ 失败'}`);
  console.log(`   前端Case Selection同步: ${frontendTest ? '✅ 通过' : '❌ 失败'}`);
  
  if (backendTest && frontendTest) {
    console.log('🎉 所有测试通过！Case Selection与聚类数据（主要诊断代码）同步功能正常');
  } else {
    console.log('⚠️ 部分测试失败，请检查相关功能');
  }
}

// 导出测试函数供浏览器控制台使用
if (typeof window !== 'undefined') {
  window.testDiagnosisCodeSync = testDiagnosisCodeSync;
  window.testCaseSelectionSync = testCaseSelectionSync;
  window.runDiagnosisCodeTests = runDiagnosisCodeTests;
  
  console.log('🧪 主要诊断代码同步测试函数已加载到全局作用域');
  console.log('💡 使用方法:');
  console.log('   - runDiagnosisCodeTests() - 运行所有测试');
  console.log('   - testDiagnosisCodeSync() - 测试后端诊断代码同步');
  console.log('   - testCaseSelectionSync() - 测试前端Case Selection同步');
}

export { testDiagnosisCodeSync, testCaseSelectionSync, runDiagnosisCodeTests };
