#!/usr/bin/env python3
"""
验证疾病关联分析API是否正常工作
"""

import requests
import json
import sys
import os

def test_disease_correlation_api():
    """测试疾病关联分析API"""
    
    # API端点
    base_url = "http://localhost:5000/api/clustering"
    correlation_url = f"{base_url}/disease-correlation"
    
    print("🧪 测试疾病关联分析API...")
    print(f"API端点: {correlation_url}")

    try:
        # 1. 测试基本连接
        print("\n1️⃣ 测试基本连接...")
        response = requests.get(f"{base_url}/status", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务连接正常")
        else:
            print(f"⚠️ 后端服务状态异常: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到后端服务: {e}")
        return False
    
    try:
        # 2. 测试疾病关联分析API
        print("\n2️⃣ 测试疾病关联分析API...")
        
        params = {
            'top_diseases': 10
        }
        
        response = requests.get(correlation_url, params=params, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API调用成功")
            
            # 验证数据结构
            print("\n📊 数据结构验证:")
            
            if 'correlation_matrix' in data:
                matrix = data['correlation_matrix']
                print(f"   关联矩阵: ✅")
                print(f"   疾病数量: {len(matrix.get('diseases', []))}")
                print(f"   矩阵大小: {matrix.get('size', 0)}x{matrix.get('size', 0)}")
                
                # 显示疾病列表
                diseases = matrix.get('diseases', [])
                if diseases:
                    print(f"   疾病列表 (前5个): {diseases[:5]}")
                
                # 检查矩阵数据
                matrix_data = matrix.get('matrix', [])
                if matrix_data:
                    print(f"   矩阵数据行数: {len(matrix_data)}")
                    if matrix_data:
                        print(f"   第一行数据长度: {len(matrix_data[0])}")
                        print(f"   数据样本: {matrix_data[0][:3] if matrix_data[0] else []}")
            else:
                print("   关联矩阵: ❌ 缺失")
            
            if 'summary' in data:
                summary = data['summary']
                print(f"   分析摘要: ✅")
                print(f"   总患者数: {summary.get('total_patients', 'N/A')}")
                print(f"   总疾病数: {summary.get('total_diseases', 'N/A')}")
                print(f"   分析方法: {summary.get('analysis_method', 'N/A')}")
            else:
                print("   分析摘要: ❌ 缺失")
            
            return True
            
        elif response.status_code == 400:
            error_data = response.json()
            print(f"❌ 请求错误: {error_data.get('error', '未知错误')}")
            return False
            
        elif response.status_code == 500:
            try:
                error_data = response.json()
                print(f"❌ 服务器错误: {error_data.get('error', '未知错误')}")
                print(f"   错误详情: {error_data.get('details', '无详情')}")
            except:
                print(f"❌ 服务器错误: {response.text}")
            return False
            
        else:
            print(f"❌ 未知响应状态: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ API调用超时")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ API调用失败: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ 响应JSON解析失败: {e}")
        print(f"   响应内容: {response.text}")
        return False

def test_clustering_data():
    """测试聚类数据是否可用"""
    
    print("\n3️⃣ 测试聚类数据可用性...")
    
    base_url = "http://localhost:5000/api/clustering"
    
    try:
        # 测试运行聚类管道
        pipeline_url = f"{base_url}/run-pipeline"
        response = requests.post(pipeline_url, json={'useDefault': True}, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 聚类数据可用")
                
                viz_data = data.get('visualizationData', [])
                print(f"   可视化数据点数量: {len(viz_data)}")
                
                if viz_data:
                    sample = viz_data[0]
                    print(f"   数据样本字段: {list(sample.keys())}")
                    
                    # 检查诊断字段
                    diagnosis_fields = ['主要诊断', '主要诊断名称', '次要诊断代码', '次要诊断名称']
                    found_fields = [field for field in diagnosis_fields if field in sample]
                    print(f"   诊断相关字段: {found_fields}")
                
                return True
            else:
                print(f"❌ 聚类管道执行失败: {data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 聚类管道调用失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 聚类数据测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始验证疾病关联分析API...")
    print("=" * 60)
    
    # 测试API
    api_test = test_disease_correlation_api()
    
    # 测试聚类数据
    clustering_test = test_clustering_data()
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print(f"   疾病关联API: {'✅ 通过' if api_test else '❌ 失败'}")
    print(f"   聚类数据可用性: {'✅ 通过' if clustering_test else '❌ 失败'}")
    
    if api_test and clustering_test:
        print("\n🎉 所有测试通过！")
        print("💡 现在可以在前端查看真实的疾病关联热力图")
        print("📋 建议操作:")
        print("   1. 刷新前端页面")
        print("   2. 等待数据加载完成")
        print("   3. 查看Disease Correlation面板")
        print("   4. 确认标题不再显示'Mock Data'")
    else:
        print("\n⚠️ 部分测试失败")
        print("📋 故障排除建议:")
        if not api_test:
            print("   - 检查后端服务是否正常运行")
            print("   - 检查API路由是否正确注册")
            print("   - 查看后端日志错误信息")
        if not clustering_test:
            print("   - 确保聚类数据文件存在")
            print("   - 检查数据文件格式是否正确")
            print("   - 验证数据加载逻辑")
    
    return api_test and clustering_test

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
