/**
 * 调试季节分布过滤功能
 * 帮助诊断为什么框选患者后季节分布图显示空白
 */

// 调试函数：检查数据结构和匹配问题
async function debugSeasonalFilter() {
  console.log('🔍 开始调试季节分布过滤功能...');
  
  try {
    // 1. 检查后端连接
    console.log('1️⃣ 检查后端连接...');
    const connectionStatus = await patientDataProcessor.checkBackendConnection();
    
    if (!connectionStatus.connected) {
      console.error('❌ 后端未连接');
      return false;
    }
    console.log('✅ 后端连接正常');
    
    // 2. 获取聚类数据
    console.log('2️⃣ 获取聚类数据...');
    const clusteringResult = await patientDataProcessor.runFullPipeline({ useDefault: true });
    
    if (!clusteringResult || !clusteringResult.visualizationData) {
      console.error('❌ 聚类数据获取失败');
      return false;
    }
    
    console.log(`✅ 聚类数据获取成功: ${clusteringResult.visualizationData.length} 个患者`);
    
    // 3. 分析患者数据结构
    console.log('3️⃣ 分析患者数据结构...');
    const samplePatients = clusteringResult.visualizationData.slice(0, 5);
    
    console.log('📋 前5个患者的数据结构:');
    samplePatients.forEach((patient, index) => {
      console.log(`患者 ${index + 1}:`, {
        patientId: patient.patientId,
        diagnosis: patient.diagnosis,
        main_diagnosis: patient.main_diagnosis,
        '主要诊断': patient['主要诊断'],
        category: patient.category,
        diseaseId: patient.diseaseId,
        cluster: patient.cluster,
        allKeys: Object.keys(patient)
      });
    });
    
    // 4. 获取季节分布数据
    console.log('4️⃣ 获取季节分布数据...');
    const seasonalData = await patientDataProcessor.getSeasonalDistribution();
    
    if (!seasonalData || !seasonalData.series) {
      console.error('❌ 季节分布数据获取失败');
      return false;
    }
    
    console.log(`✅ 季节分布数据获取成功: ${seasonalData.series.length} 个疾病系列`);
    
    // 5. 分析季节分布数据结构
    console.log('5️⃣ 分析季节分布数据结构...');
    console.log('📊 季节分布数据结构:', {
      xAxis: seasonalData.xAxis,
      seriesCount: seasonalData.series.length,
      sampleSeries: seasonalData.series.slice(0, 3).map(s => ({
        name: s.name,
        type: s.type,
        dataLength: s.data?.length
      }))
    });
    
    console.log('📋 所有疾病名称:');
    seasonalData.series.forEach((series, index) => {
      console.log(`  ${index + 1}. "${series.name}"`);
    });
    
    // 6. 提取患者诊断信息
    console.log('6️⃣ 提取患者诊断信息...');
    const patientDiagnoses = clusteringResult.visualizationData.map(patient => {
      const diagnosis = patient.diagnosis || 
                       patient.main_diagnosis || 
                       patient['主要诊断'] ||
                       patient.category ||
                       patient.diseaseId;
      return {
        patientId: patient.patientId,
        diagnosis: diagnosis
      };
    }).filter(p => p.diagnosis);
    
    console.log(`📋 有诊断信息的患者: ${patientDiagnoses.length} 个`);
    
    // 统计诊断分布
    const diagnosisCount = {};
    patientDiagnoses.forEach(p => {
      diagnosisCount[p.diagnosis] = (diagnosisCount[p.diagnosis] || 0) + 1;
    });
    
    console.log('📊 患者诊断分布:');
    Object.entries(diagnosisCount).forEach(([diagnosis, count]) => {
      console.log(`  "${diagnosis}": ${count} 个患者`);
    });
    
    // 7. 检查匹配情况
    console.log('7️⃣ 检查诊断匹配情况...');
    const seasonalDiseases = new Set(seasonalData.series.map(s => s.name));
    const patientDiseaseSet = new Set(Object.keys(diagnosisCount));
    
    console.log('🔍 匹配分析:');
    console.log(`  季节分布中的疾病数量: ${seasonalDiseases.size}`);
    console.log(`  患者数据中的诊断数量: ${patientDiseaseSet.size}`);
    
    // 找出匹配的疾病
    const matchedDiseases = [...patientDiseaseSet].filter(disease => 
      seasonalDiseases.has(disease)
    );
    
    console.log(`  匹配的疾病数量: ${matchedDiseases.length}`);
    console.log('  匹配的疾病:', matchedDiseases);
    
    // 找出不匹配的疾病
    const unmatchedPatientDiseases = [...patientDiseaseSet].filter(disease => 
      !seasonalDiseases.has(disease)
    );
    
    const unmatchedSeasonalDiseases = [...seasonalDiseases].filter(disease => 
      !patientDiseaseSet.has(disease)
    );
    
    if (unmatchedPatientDiseases.length > 0) {
      console.log('⚠️ 患者数据中未匹配的诊断:', unmatchedPatientDiseases);
    }
    
    if (unmatchedSeasonalDiseases.length > 0) {
      console.log('⚠️ 季节分布中未匹配的疾病:', unmatchedSeasonalDiseases);
    }
    
    // 8. 模拟过滤测试
    console.log('8️⃣ 模拟过滤测试...');
    
    // 选择前10个有诊断的患者
    const testPatients = patientDiagnoses.slice(0, 10);
    const testDiagnoses = testPatients.map(p => p.diagnosis);
    
    console.log('🧪 测试患者诊断:', testDiagnoses);
    
    // 模拟过滤逻辑
    const testDiagnosisSet = new Set(testDiagnoses);
    const filteredSeries = seasonalData.series.filter(series => 
      testDiagnosisSet.has(series.name)
    );
    
    console.log(`🧪 过滤结果: ${filteredSeries.length} 个匹配的系列`);
    console.log('🧪 匹配的系列:', filteredSeries.map(s => s.name));
    
    // 9. 提供修复建议
    console.log('9️⃣ 修复建议...');
    
    if (matchedDiseases.length === 0) {
      console.log('❌ 问题诊断: 患者诊断与季节分布疾病完全不匹配');
      console.log('💡 建议解决方案:');
      console.log('   1. 检查患者数据中的诊断字段名称');
      console.log('   2. 检查季节分布数据中的疾病名称格式');
      console.log('   3. 添加诊断名称映射逻辑');
      console.log('   4. 确认后端数据一致性');
    } else if (matchedDiseases.length < patientDiseaseSet.size / 2) {
      console.log('⚠️ 问题诊断: 大部分患者诊断与季节分布疾病不匹配');
      console.log('💡 建议解决方案:');
      console.log('   1. 添加诊断名称标准化逻辑');
      console.log('   2. 检查数据编码格式');
      console.log('   3. 实现模糊匹配算法');
    } else {
      console.log('✅ 数据匹配情况良好');
      console.log('💡 可能的问题:');
      console.log('   1. 选中的患者恰好没有匹配的诊断');
      console.log('   2. 前端过滤逻辑有误');
      console.log('   3. 事件传递有问题');
    }
    
    return {
      clusteringData: clusteringResult,
      seasonalData: seasonalData,
      matchedDiseases: matchedDiseases,
      patientDiagnoses: patientDiagnoses,
      diagnosisCount: diagnosisCount
    };
    
  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error);
    return false;
  }
}

// 调试函数：测试具体的过滤逻辑
async function testFilterLogic() {
  console.log('🧪 测试过滤逻辑...');
  
  try {
    const debugResult = await debugSeasonalFilter();
    
    if (!debugResult) {
      console.error('❌ 无法获取调试数据');
      return;
    }
    
    const { clusteringData, seasonalData, matchedDiseases } = debugResult;
    
    // 创建测试用的患者选择
    const testSelections = [
      // 测试1: 选择有匹配诊断的患者
      {
        name: '有匹配诊断的患者',
        patients: clusteringData.visualizationData.filter(p => {
          const diagnosis = p.diagnosis || p.main_diagnosis || p['主要诊断'] || p.category;
          return matchedDiseases.includes(diagnosis);
        }).slice(0, 5)
      },
      // 测试2: 选择特定诊断的患者
      {
        name: '特定诊断的患者',
        patients: clusteringData.visualizationData.filter(p => {
          const diagnosis = p.diagnosis || p.main_diagnosis || p['主要诊断'] || p.category;
          return diagnosis === matchedDiseases[0];
        }).slice(0, 3)
      },
      // 测试3: 混合选择
      {
        name: '混合选择',
        patients: clusteringData.visualizationData.slice(0, 10)
      }
    ];
    
    testSelections.forEach((test, index) => {
      console.log(`\n🧪 测试 ${index + 1}: ${test.name}`);
      console.log(`   选中患者数量: ${test.patients.length}`);
      
      if (test.patients.length === 0) {
        console.log('   ⚠️ 没有患者可供测试');
        return;
      }
      
      // 提取诊断
      const diagnoses = test.patients.map(p => {
        return p.diagnosis || p.main_diagnosis || p['主要诊断'] || p.category;
      }).filter(d => d);
      
      console.log(`   提取到的诊断: ${diagnoses}`);
      
      // 过滤季节数据
      const diagnosisSet = new Set(diagnoses);
      const filteredSeries = seasonalData.series.filter(series => 
        diagnosisSet.has(series.name)
      );
      
      console.log(`   过滤结果: ${filteredSeries.length} 个系列`);
      console.log(`   匹配的疾病: ${filteredSeries.map(s => s.name)}`);
      
      if (filteredSeries.length === 0) {
        console.log('   ❌ 过滤结果为空，这会导致图表空白');
      } else {
        console.log('   ✅ 过滤结果正常');
      }
    });
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 主调试函数
async function runSeasonalFilterDebug() {
  console.log('🚀 开始运行季节分布过滤调试...');
  console.log('=' * 60);
  
  const debugResult = await debugSeasonalFilter();
  
  console.log('\n' + '=' * 60);
  
  if (debugResult) {
    await testFilterLogic();
  }
  
  console.log('\n' + '=' * 60);
  console.log('🏁 调试完成');
  
  if (debugResult && debugResult.matchedDiseases.length > 0) {
    console.log('💡 下一步操作建议:');
    console.log('   1. 在浏览器中框选患者');
    console.log('   2. 查看控制台输出的调试信息');
    console.log('   3. 检查患者诊断是否与季节分布疾病匹配');
    console.log('   4. 如果仍然空白，检查前端过滤逻辑');
  }
}

// 导出调试函数供浏览器控制台使用
if (typeof window !== 'undefined') {
  window.debugSeasonalFilter = debugSeasonalFilter;
  window.testFilterLogic = testFilterLogic;
  window.runSeasonalFilterDebug = runSeasonalFilterDebug;
  
  console.log('🔍 季节分布过滤调试函数已加载到全局作用域');
  console.log('💡 使用方法:');
  console.log('   - runSeasonalFilterDebug() - 运行完整调试');
  console.log('   - debugSeasonalFilter() - 基础数据结构调试');
  console.log('   - testFilterLogic() - 测试过滤逻辑');
}

export { debugSeasonalFilter, testFilterLogic, runSeasonalFilterDebug };
