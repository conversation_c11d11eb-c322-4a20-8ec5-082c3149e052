# Apriori关联规则疾病关联分析使用指南

## 功能概述

本功能使用Apriori关联规则算法分析聚类数据中主要诊断和次要诊断代码的关系，生成疾病关联热力图，帮助发现疾病间的潜在关联模式。

## 🎯 核心特性

### Apriori算法分析
- **关联规则挖掘**: 发现疾病间的"如果-那么"关系
- **支持度计算**: 衡量疾病组合的频繁程度
- **置信度评估**: 评估关联规则的可靠性
- **提升度分析**: 判断关联关系的强度

### 疾病关联热力图
- **可视化展示**: 直观显示疾病间的关联强度
- **交互式探索**: 鼠标悬停查看详细关联信息
- **颜色编码**: 使用渐变色表示关联强度
- **实时更新**: 与聚类数据同步更新

### 数据集成
- **真实数据**: 基于实际的聚类数据进行分析
- **动态分析**: 聚类方法改变时自动重新分析
- **参数可调**: 支持调整算法参数获得不同分析结果

## 📊 算法原理

### Apriori算法步骤

1. **事务准备**
   ```
   患者记录 → 疾病事务
   主要诊断: H35.804
   次要诊断: H40.11, H25.9
   ↓
   事务: {H35.804, H40.11, H25.9}
   ```

2. **频繁项集挖掘**
   ```
   1-项集: {H35.804}, {H40.11}, {H25.9}
   2-项集: {H35.804, H40.11}, {H35.804, H25.9}
   3-项集: {H35.804, H40.11, H25.9}
   ```

3. **关联规则生成**
   ```
   规则: H35.804 → H40.11
   支持度: 0.15 (15%的患者同时患有这两种疾病)
   置信度: 0.75 (75%患有H35.804的患者也患有H40.11)
   提升度: 2.5 (关联强度是随机情况的2.5倍)
   ```

### 关键指标说明

#### 支持度 (Support)
- **定义**: 包含特定疾病组合的患者比例
- **计算**: Support(A,B) = P(A ∩ B)
- **意义**: 衡量疾病组合的普遍性

#### 置信度 (Confidence)  
- **定义**: 在患有疾病A的患者中，同时患有疾病B的比例
- **计算**: Confidence(A→B) = P(B|A) = P(A ∩ B) / P(A)
- **意义**: 衡量关联规则的可靠性

#### 提升度 (Lift)
- **定义**: 关联规则的置信度与B的先验概率的比值
- **计算**: Lift(A→B) = P(B|A) / P(B)
- **意义**: 衡量关联关系的强度
  - Lift = 1: 无关联
  - Lift > 1: 正关联
  - Lift < 1: 负关联

## 🔧 技术实现

### 后端实现

#### AprioriAnalyzer类
```python
class AprioriAnalyzer:
    def __init__(self, min_support=0.01, min_confidence=0.1, min_lift=1.0):
        self.min_support = min_support
        self.min_confidence = min_confidence  
        self.min_lift = min_lift
    
    def analyze(self, data):
        # 准备事务数据
        self.prepare_transactions(data)
        
        # 挖掘频繁项集
        frequent_itemsets = self.find_all_frequent_itemsets()
        
        # 生成关联规则
        association_rules = self.generate_association_rules()
        
        # 创建关联矩阵
        correlation_matrix = self.create_correlation_matrix()
        
        return {
            'frequent_itemsets': frequent_itemsets,
            'association_rules': association_rules,
            'correlation_matrix': correlation_matrix
        }
```

#### API端点
```python
@bp.route('/disease-correlation', methods=['GET'])
def get_disease_correlation():
    # 获取参数
    min_support = float(request.args.get('min_support', 0.01))
    min_confidence = float(request.args.get('min_confidence', 0.1))
    min_lift = float(request.args.get('min_lift', 1.0))
    
    # 执行分析
    analyzer = AprioriAnalyzer(min_support, min_confidence, min_lift)
    result = analyzer.analyze(processor.raw_data)
    
    return jsonify(result)
```

### 前端实现

#### 数据获取
```javascript
// 获取疾病关联分析数据
async getDiseaseCorrelation(options = {}) {
  const params = new URLSearchParams({
    min_support: options.minSupport || 0.01,
    min_confidence: options.minConfidence || 0.1,
    min_lift: options.minLift || 1.0,
    top_diseases: options.topDiseases || 15
  });

  const response = await fetch(`${this.baseURL}/disease-correlation?${params}`);
  return response.json();
}
```

#### 热力图生成
```javascript
generateRealCorrelationOption(correlationMatrix) {
  const diseases = correlationMatrix.diseases;
  const matrix = correlationMatrix.matrix;
  
  // 创建热力图数据
  const heatmapData = [];
  for (let i = 0; i < diseases.length; i++) {
    for (let j = 0; j < diseases.length; j++) {
      heatmapData.push([i, j, matrix[i][j]]);
    }
  }

  return {
    title: { text: 'Disease Correlation (Apriori Analysis)' },
    xAxis: { type: "category", data: diseases },
    yAxis: { type: "category", data: diseases },
    series: [{
      name: "Disease Correlation",
      type: "heatmap", 
      data: heatmapData
    }]
  };
}
```

## 📋 使用步骤

### 1. 系统准备
```bash
# 启动后端服务
cd Bibm-master/Flask_end
python app.py

# 启动前端服务
cd Bibm-master
npm run dev
```

### 2. 数据加载
- 确保聚类数据已加载
- 系统自动执行Apriori分析
- 生成疾病关联矩阵

### 3. 查看热力图
- 在Disease Correlation面板查看热力图
- 颜色深浅表示关联强度
- 鼠标悬停查看详细信息

### 4. 解读结果
- **深色区域**: 强关联关系
- **浅色区域**: 弱关联关系
- **对角线**: 自相关（值为1.0）

## 🎨 热力图特性

### 视觉设计
- **颜色方案**: 蓝色渐变，从浅蓝到深蓝
- **标签显示**: 显示具体的关联值
- **交互提示**: 详细的tooltip信息
- **自适应布局**: 根据疾病数量调整大小

### 交互功能
- **鼠标悬停**: 显示疾病对和关联强度
- **缩放平移**: 支持图表缩放和平移
- **数据导出**: 可导出关联数据

## 📈 参数调整

### 支持度阈值
- **低值 (0.005-0.01)**: 发现更多关联，包括罕见组合
- **中值 (0.01-0.05)**: 平衡发现能力和结果质量
- **高值 (0.05+)**: 只保留最频繁的关联

### 置信度阈值
- **低值 (0.1-0.3)**: 包含更多潜在关联
- **中值 (0.3-0.6)**: 中等可靠性的关联
- **高值 (0.6+)**: 只保留高可靠性关联

### 提升度阈值
- **1.0**: 基准值，表示无关联
- **1.5+**: 中等强度关联
- **2.0+**: 强关联关系

## 🔍 结果解读

### 关联强度分级
- **0.8-1.0**: 极强关联
- **0.6-0.8**: 强关联
- **0.4-0.6**: 中等关联
- **0.2-0.4**: 弱关联
- **0.0-0.2**: 极弱关联

### 临床意义
- **强关联**: 可能存在病理生理联系
- **中等关联**: 值得进一步研究
- **弱关联**: 可能是偶然关联

## 🧪 测试验证

### 浏览器控制台测试
```javascript
// 运行完整测试
runAprioriCorrelationTests();

// 单独测试功能
testAprioriCorrelationAnalysis();
testHeatmapIntegration();
```

### 验证要点
1. **数据完整性**: 确保所有疾病都包含在分析中
2. **算法正确性**: 验证支持度、置信度、提升度计算
3. **可视化准确性**: 确保热力图正确反映关联强度
4. **性能表现**: 验证大数据集的处理能力

## 🚀 应用场景

### 临床研究
- **并发症分析**: 发现疾病间的并发关系
- **治疗方案**: 基于关联关系制定治疗策略
- **风险评估**: 预测患者可能的并发疾病

### 数据挖掘
- **模式发现**: 识别隐藏的疾病关联模式
- **假设生成**: 为进一步研究提供假设
- **知识发现**: 从大量数据中提取有价值信息

### 决策支持
- **诊断辅助**: 基于已知疾病推荐检查项目
- **资源配置**: 根据疾病关联优化医疗资源
- **预防策略**: 制定针对性的预防措施

---

通过Apriori关联规则分析，您可以深入了解疾病间的复杂关系，为临床决策和医学研究提供有力的数据支持！
